{"version": 3, "file": "callback_workflow.js", "sourceRoot": "", "sources": ["../../../../src/cmap/auth/mongodb_oidc/callback_workflow.ts"], "names": [], "mappings": ";;;AAAA,+BAAmD;AAEnD,0CAA+F;AAC/F,0CAAoC;AAWpC,4CAA6C;AAC7C,+DAA0D;AAC1D,2DAAsD;AAEtD,kDAAkD;AAClD,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,2BAA2B;AAC3B,MAAM,SAAS,GAAG,GAAG,CAAC;AAEtB,kDAAkD;AAClD,MAAM,iBAAiB,GAAG,CAAC,aAAa,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;AAE9E,yDAAyD;AACzD,MAAM,qBAAqB,GACzB,8EAA8E,CAAC;AAEjF;;;GAGG;AACH,MAAa,gBAAgB;IAI3B;;OAEG;IACH;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,mCAAe,EAAE,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAiB,EAAE,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe,CAAC,WAA6B;QACjD,MAAM,QAAQ,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACnD,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC;QACjC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,UAAsB,EACtB,WAA6B,EAC7B,gBAAyB,EACzB,QAAmB;QAEnB,6DAA6D;QAC7D,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CACpF,UAAU,EACV,WAAW,CACZ,CAAC;QACF,2CAA2C;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC1F,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,EAAE;YACT,0EAA0E;YAC1E,4DAA4D;YAC5D,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACxC,2EAA2E;gBAC3E,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CACtC,UAAU,EACV,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,QAAQ,EAAE,uBAAuB,EAAE,cAAc,CAClD,CAAC;aACH;iBAAM;gBACL,uEAAuE;gBACvE,+BAA+B;gBAC/B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC7C,UAAU,EACV,WAAW,EACX,KAAK,CAAC,UAAU,EAChB,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,eAAe,CAChB,CAAC;gBACF,IAAI;oBACF,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CACtC,UAAU,EACV,WAAW,EACX,WAAW,EACX,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,uBAAuB,EAAE,cAAc,CACjF,CAAC;iBACH;gBAAC,OAAO,KAAK,EAAE;oBACd,mEAAmE;oBACnE,kEAAkE;oBAClE,mBAAmB;oBACnB,IACE,gBAAgB;wBAChB,KAAK,YAAY,kBAAU;wBAC3B,KAAK,CAAC,IAAI,KAAK,2BAAmB,CAAC,cAAc,EACjD;wBACA,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;wBAC/E,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;qBACxE;yBAAM;wBACL,MAAM,KAAK,CAAC;qBACb;iBACF;aACF;SACF;aAAM;YACL,mEAAmE;YACnE,uEAAuE;YACvE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAClD,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,QAAQ,CACT,CAAC;YACF,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;YACpD,MAAM,YAAY,GAAG,WAAI,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAkB,CAAC;YACrF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC7C,UAAU,EACV,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,eAAe,CAChB,CAAC;YACF,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CACtC,UAAU,EACV,WAAW,EACX,WAAW,EACX,cAAc,CACf,CAAC;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,mBAAmB,CAC/B,UAAsB,EACtB,WAA6B,EAC7B,gBAAyB,EACzB,QAAmB;QAEnB,IAAI,MAAM,CAAC;QACX,IAAI,CAAC,gBAAgB,IAAI,QAAQ,EAAE,uBAAuB,EAAE;YAC1D,MAAM,GAAG,QAAQ,CAAC,uBAAuB,CAAC;SAC3C;aAAM;YACL,MAAM,GAAG,MAAM,UAAU,CAAC,YAAY,CACpC,IAAA,UAAE,EAAC,WAAW,CAAC,MAAM,CAAC,EACtB,oBAAoB,CAAC,WAAW,CAAC,EACjC,SAAS,CACV,CAAC;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,UAAsB,EACtB,WAA6B,EAC7B,WAA8B,EAC9B,cAAuB;QAEvB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,YAAY,CAC1C,IAAA,UAAE,EAAC,WAAW,CAAC,MAAM,CAAC,EACtB,qBAAqB,CAAC,WAAW,CAAC,WAAW,EAAE,cAAc,CAAC,EAC9D,SAAS,CACV,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,gBAAgB,CAC5B,UAAsB,EACtB,WAA6B,EAC7B,UAAyB,EACzB,gBAAyB,EACzB,YAAoB,EACpB,eAAoC,EACpC,eAAqC;QAErC,gCAAgC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC1F,IAAI,MAAM,CAAC;QACX,MAAM,OAAO,GAAwB,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;QAC1F,yCAAyC;QACzC,IAAI,KAAK,EAAE;YACT,wDAAwD;YACxD,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACxC,OAAO,KAAK,CAAC,WAAW,CAAC;aAC1B;YACD,8EAA8E;YAC9E,yEAAyE;YACzE,iDAAiD;YACjD,IAAI,eAAe,EAAE;gBACnB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC;gBACtD,MAAM,GAAG,MAAM,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;aACrD;iBAAM;gBACL,MAAM,GAAG,MAAM,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;aACrD;SACF;aAAM;YACL,0DAA0D;YAC1D,MAAM,GAAG,MAAM,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACrD;QACD,gFAAgF;QAChF,iDAAiD;QACjD,IAAI,uBAAuB,CAAC,MAAM,CAAC,EAAE;YACnC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAC/E,MAAM,IAAI,oCAA4B,CAAC,qBAAqB,CAAC,CAAC;SAC/D;QACD,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAClC,oCAAoC;QACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CACjB,UAAU,CAAC,OAAO,EAClB,WAAW,CAAC,QAAQ,IAAI,EAAE,EAC1B,YAAY,EACZ,MAAM,EACN,UAAU,CACX,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAlND,4CAkNC;AAED;;;GAGG;AACH,SAAS,qBAAqB,CAAC,KAAa,EAAE,cAAuB;IACnE,IAAI,cAAc,IAAI,IAAI,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;QAChE,OAAO;YACL,YAAY,EAAE,CAAC;YACf,cAAc,EAAE,cAAc;YAC9B,OAAO,EAAE,IAAI,aAAM,CAAC,WAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;SACpD,CAAC;KACH;IACD,+EAA+E;IAC/E,8EAA8E;IAC9E,+EAA+E;IAC/E,gCAAgC;IAChC,OAAO;QACL,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,yBAAa,CAAC,YAAY;QACrC,OAAO,EAAE,IAAI,aAAM,CAAC,WAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;KACpD,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,uBAAuB,CAAC,WAAoB;IACnD,IAAI,WAAW,IAAI,IAAI,IAAI,OAAO,WAAW,KAAK,QAAQ;QAAE,OAAO,IAAI,CAAC;IACxE,IAAI,CAAC,CAAC,aAAa,IAAI,WAAW,CAAC;QAAE,OAAO,IAAI,CAAC;IACjD,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAClG,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,WAA6B;IACzD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,WAAW,CAAC,QAAQ,EAAE;QACxB,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC;KAClC;IACD,OAAO;QACL,SAAS,EAAE,CAAC;QACZ,aAAa,EAAE,CAAC;QAChB,SAAS,EAAE,yBAAa,CAAC,YAAY;QACrC,OAAO,EAAE,IAAI,aAAM,CAAC,WAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAC7C,CAAC;AACJ,CAAC"}