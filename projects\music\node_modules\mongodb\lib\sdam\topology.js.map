{"version": 3, "file": "topology.js", "sourceRoot": "", "sources": ["../../src/sdam/topology.ts"], "names": [], "mappings": ";;;AAAA,mCAAkD;AAClD,+BAAiC;AAOjC,4DAAsE;AACtE,4CAcsB;AACtB,oCAQkB;AAElB,gDAAmD;AACnD,wDAA6E;AAG7E,oCAQkB;AAClB,qCAWkB;AAClB,qCAOkB;AAClB,qCAAyE;AACzE,6DAAiF;AACjF,yDAAuF;AACvF,+CAAgE;AAChE,iEAA6D;AAE7D,eAAe;AACf,IAAI,qBAAqB,GAAG,CAAC,CAAC;AAE9B,MAAM,eAAe,GAAG,IAAA,wBAAgB,EAAC;IACvC,CAAC,qBAAY,CAAC,EAAE,CAAC,qBAAY,EAAE,yBAAgB,CAAC;IAChD,CAAC,yBAAgB,CAAC,EAAE,CAAC,yBAAgB,EAAE,sBAAa,EAAE,wBAAe,EAAE,qBAAY,CAAC;IACpF,CAAC,wBAAe,CAAC,EAAE,CAAC,wBAAe,EAAE,sBAAa,EAAE,qBAAY,CAAC;IACjE,CAAC,sBAAa,CAAC,EAAE,CAAC,sBAAa,EAAE,qBAAY,CAAC;CAC/C,CAAC,CAAC;AAEH,gBAAgB;AAChB,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AACvC,gBAAgB;AAChB,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AAgGvC;;;GAGG;AACH,MAAa,QAAS,SAAQ,+BAAiC;IAwC7D;;OAEG;IACH,YACE,MAAmB,EACnB,KAAsD,EACtD,OAAwB;QAExB,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAA,gBAAS,EAChC,CACE,QAAkD,EAClD,OAA4B,EAC5B,QAAuC,EACvC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAe,CAAC,CAC3D,CAAC;QAEF,0FAA0F;QAC1F,OAAO,GAAG,OAAO,IAAI;YACnB,KAAK,EAAE,CAAC,mBAAW,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAClD,GAAG,MAAM,CAAC,WAAW,CAAC,mCAAe,CAAC,OAAO,EAAE,CAAC;YAChD,GAAG,MAAM,CAAC,WAAW,CAAC,iCAAa,CAAC,OAAO,EAAE,CAAC;SAC/C,CAAC;QAEF,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,KAAK,GAAG,CAAC,mBAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACzC;aAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAChC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;SACjB;QAED,MAAM,QAAQ,GAAkB,EAAE,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,QAAQ,CAAC,IAAI,CAAC,mBAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;aAC7C;iBAAM,IAAI,IAAI,YAAY,mBAAW,EAAE;gBACtC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACrB;iBAAM;gBACL,qDAAqD;gBACrD,MAAM,IAAI,yBAAiB,CAAC,uCAAuC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAC5F;SACF;QAED,MAAM,YAAY,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,UAAU,GAAG,qBAAqB,EAAE,CAAC;QAE3C,MAAM,aAAa,GACjB,OAAO,CAAC,WAAW,IAAI,IAAI;YAC3B,OAAO,CAAC,WAAW,KAAK,CAAC;YACzB,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM;YACpC,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,IAAA,eAAO,EAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QAE7C,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,KAAK,MAAM,WAAW,IAAI,aAAa,EAAE;YACvC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,IAAI,sCAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,YAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,CAAC,GAAG;YACP,0BAA0B;YAC1B,EAAE,EAAE,UAAU;YACd,oBAAoB;YACpB,OAAO;YACP,4CAA4C;YAC5C,QAAQ;YACR,gBAAgB;YAChB,KAAK,EAAE,qBAAY;YACnB,2BAA2B;YAC3B,WAAW,EAAE,IAAI,0CAAmB,CAClC,YAAY,EACZ,kBAAkB,EAClB,OAAO,CAAC,UAAU,EAClB,SAAS,EACT,SAAS,EACT,SAAS,EACT,OAAO,CACR;YACD,wBAAwB,EAAE,OAAO,CAAC,wBAAwB;YAC1D,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;YACxD,oDAAoD;YACpD,OAAO,EAAE,IAAI,GAAG,EAAE;YAClB,WAAW,EAAE,OAAO,EAAE,WAAW;YACjC,WAAW,EAAE,SAAS;YAEtB,mBAAmB;YACnB,gBAAgB,EAAE,IAAI,GAAG,EAAkB;YAC3C,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC3D,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;SAClD,CAAC;QAEF,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,CAAC,CAAC,SAAS;gBACd,OAAO,CAAC,SAAS;oBACjB,IAAI,uBAAS,CAAC;wBACZ,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC,oBAAoB;wBACjD,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,cAAc,EAAE,OAAO,CAAC,cAAc;qBACvC,CAAC,CAAC;YAEL,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;SAC9E;IACH,CAAC;IAEO,qBAAqB,CAAC,KAAsC;QAClE,MAAM,YAAY,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;QAE1C,MAAM,mBAAmB,GACvB,YAAY,KAAK,qBAAY,CAAC,OAAO,IAAI,OAAO,KAAK,qBAAY,CAAC,OAAO,CAAC;QAC5E,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,uBAAS,CAAC,oBAAoB,CAAC,CAAC;QACjF,MAAM,qBAAqB,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAEhF,IAAI,mBAAmB,IAAI,CAAC,qBAAqB,EAAE;YACjD,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,uBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;YAC9E,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;SAC3B;IACH,CAAC;IAEO,gBAAgB,CAAC,EAAmB;QAC1C,MAAM,2BAA2B,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;QACvD,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,yBAAyB,CAC/D,EAAE,EACF,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAC3B,CAAC;QACF,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,KAAK,2BAA2B,EAAE;YACtD,6BAA6B;YAC7B,OAAO;SACR;QAED,aAAa,CAAC,IAAI,CAAC,CAAC;QAEpB,IAAI,CAAC,IAAI,CACP,QAAQ,CAAC,4BAA4B,EACrC,IAAI,wCAA+B,CACjC,IAAI,CAAC,CAAC,CAAC,EAAE,EACT,2BAA2B,EAC3B,IAAI,CAAC,CAAC,CAAC,WAAW,CACnB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IACrC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IAClD,CAAC;IAKD,OAAO,CAAC,OAAmC,EAAE,QAAmB;QAC9D,IAAI,OAAO,OAAO,KAAK,UAAU;YAAE,CAAC,QAAQ,GAAG,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QACxE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,wBAAe,EAAE;YACpC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,QAAQ,EAAE,CAAC;aACZ;YAED,OAAO;SACR;QAED,eAAe,CAAC,IAAI,EAAE,yBAAgB,CAAC,CAAC;QAExC,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,6BAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1E,wCAAwC;QACxC,IAAI,CAAC,IAAI,CACP,QAAQ,CAAC,4BAA4B,EACrC,IAAI,wCAA+B,CACjC,IAAI,CAAC,CAAC,CAAC,EAAE,EACT,IAAI,0CAAmB,CAAC,qBAAY,CAAC,OAAO,CAAC,EAAE,4BAA4B;QAC3E,IAAI,CAAC,CAAC,CAAC,WAAW,CACnB,CACF,CAAC;QAEF,sEAAsE;QACtE,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3E,IAAI,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,GAAG,CACtB,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC1C,iBAAiB,CAAC,OAAO;YACzB,sBAAsB,CAAC,IAAI,EAAE,iBAAiB,CAAC;SAChD,CAAC,CACH,CAAC;QAEF,qEAAqE;QACrE,6DAA6D;QAC7D,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE;YAC/B,KAAK,MAAM,WAAW,IAAI,kBAAkB,EAAE;gBAC5C,MAAM,cAAc,GAAG,IAAI,sCAAiB,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE;oBAC/E,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY;iBAC1C,CAAC,CAAC;gBACH,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;aAC1C;SACF;QAED,MAAM,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE,CACrC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAEhE,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,gCAAc,CAAC,OAAO,CAAC;QACxE,IAAI,CAAC,YAAY,CAAC,IAAA,+CAA4B,EAAC,cAAc,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACvF,IAAI,GAAG,EAAE;gBACP,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/D;YAED,kBAAkB;YAClB,MAAM,iBAAiB,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI,CAAC;YACzF,IAAI,CAAC,iBAAiB,IAAI,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE;gBACtD,MAAM,CAAC,OAAO,CAAC,IAAA,UAAE,EAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE;oBACtD,IAAI,GAAG,EAAE;wBACP,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC;qBAC3B;oBAED,eAAe,CAAC,IAAI,EAAE,wBAAe,CAAC,CAAC;oBACvC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAElC,QAAQ,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;gBAEH,OAAO;aACR;YAED,eAAe,CAAC,IAAI,EAAE,wBAAe,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAElC,QAAQ,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,OAAsB,EAAE,QAAmB;QAC/C,OAAO,GAAG,OAAO,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,qBAAY,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,sBAAa,EAAE;YACnE,OAAO,QAAQ,EAAE,EAAE,CAAC;SACrB;QAED,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;YACpE,OAAO,IAAA,gBAAS,EAAC,aAAa,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;aAC1B,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAEvB,eAAe,CAAC,IAAI,EAAE,sBAAa,CAAC,CAAC;YAErC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,gCAAwB,EAAE,CAAC,CAAC;YACjE,IAAA,wBAAe,EAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;YAEzC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE;gBACpB,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;gBACxB,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,uBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;aAC1F;YAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;YAEzF,eAAe,CAAC,IAAI,EAAE,qBAAY,CAAC,CAAC;YAEpC,0BAA0B;YAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,4BAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC;aACD,OAAO,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CACV,QAAkD,EAClD,OAA4B,EAC5B,QAA0B;QAE1B,IAAI,cAAc,CAAC;QACnB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,cAAc,GAAG,IAAA,+CAA4B,EAAC,gCAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;aACpF;iBAAM;gBACL,IAAI,cAAc,CAAC;gBACnB,IAAI,QAAQ,YAAY,gCAAc,EAAE;oBACtC,cAAc,GAAG,QAAQ,CAAC;iBAC3B;qBAAM;oBACL,gCAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAClC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,gCAAc,CAAC,OAAO,CAAC;iBACnE;gBAED,cAAc,GAAG,IAAA,+CAA4B,EAAC,cAAgC,CAAC,CAAC;aACjF;SACF;aAAM;YACL,cAAc,GAAG,QAAQ,CAAC;SAC3B;QAED,OAAO,GAAG,MAAM,CAAC,MAAM,CACrB,EAAE,EACF,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC,wBAAwB,EAAE,EAC7D,OAAO,CACR,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAY,CAAC,OAAO,CAAC;QACjE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,MAAM,WAAW,GAAG,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC;QAEnD,IAAI,SAAS,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,EAAE;YAClD,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YACxC,OAAO;SACR;QAED,MAAM,eAAe,GAA2B;YAC9C,cAAc;YACd,WAAW;YACX,QAAQ;SACT,CAAC;QAEF,MAAM,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;QAClE,IAAI,wBAAwB,EAAE;YAC5B,eAAe,CAAC,KAAK,GAAG,IAAA,mBAAU,EAAC,GAAG,EAAE;gBACtC,eAAe,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBACnC,eAAe,CAAC,KAAK,GAAG,SAAS,CAAC;gBAClC,MAAM,YAAY,GAAG,IAAI,iCAAyB,CAChD,oCAAoC,wBAAwB,KAAK,EACjE,IAAI,CAAC,WAAW,CACjB,CAAC;gBAEF,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACzC,CAAC,EAAE,wBAAwB,CAAC,CAAC;SAC9B;QAED,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAC,iBAAoC;QACtD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;YAC5D,OAAO;SACR;QAED,oEAAoE;QACpE,IAAI,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC,EAAE;YACnE,OAAO;SACR;QAED,iDAAiD;QACjD,MAAM,2BAA2B,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;QACvD,MAAM,yBAAyB,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC5F,IAAI,CAAC,yBAAyB,EAAE;YAC9B,OAAO;SACR;QAED,wEAAwE;QACxE,uEAAuE;QACvE,iEAAiE;QACjE,wEAAwE;QACxE,oEAAoE;QACpE,4CAA4C;QAC5C,MAAM,WAAW,GAAG,iBAAiB,CAAC,YAAY,CAAC;QACnD,IAAI,WAAW,EAAE;YACf,IAAA,4BAAmB,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SACxC;QAED,qFAAqF;QACrF,wFAAwF;QACxF,yFAAyF;QACzF,MAAM,iBAAiB,GACrB,yBAAyB,IAAI,yBAAyB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAEnF,uCAAuC;QACvC,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,kBAAkB,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,+BAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC9F,OAAO;SACR;QAED,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,EAAE;YACtB,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACjF,IAAI,cAAc,EAAE;gBAClB,IAAI,CAAC,IAAI,CACP,QAAQ,CAAC,0BAA0B,EACnC,IAAI,sCAA6B,CAC/B,IAAI,CAAC,CAAC,CAAC,EAAE,EACT,iBAAiB,CAAC,OAAO,EACzB,yBAAyB,EACzB,cAAc,CACf,CACF,CAAC;aACH;SACF;QAED,+CAA+C;QAC/C,aAAa,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAEvC,+DAA+D;QAC/D,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,gBAAgB,CAAC,IAAI,CAAC,CAAC;SACxB;QAED,IAAI,CAAC,iBAAiB,EAAE;YACtB,IAAI,CAAC,IAAI,CACP,QAAQ,CAAC,4BAA4B,EACrC,IAAI,wCAA+B,CACjC,IAAI,CAAC,CAAC,CAAC,EAAE,EACT,2BAA2B,EAC3B,IAAI,CAAC,CAAC,CAAC,WAAW,CACnB,CACF,CAAC;SACH;IACH,CAAC;IAED,IAAI,CAAC,WAA8B,EAAE,QAAmB;QACtD,IAAI,OAAO,WAAW,KAAK,UAAU;YAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,EAAE,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC;QAC3F,IAAI,OAAO,QAAQ,KAAK,UAAU;YAAE,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,wBAAe,CAAC;IAC1C,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,qBAAY,CAAC;IACvC,CAAC;IAED,+EAA+E;IAC/E,oFAAoF;IACpF,4EAA4E;IAC5E,SAAS;QACP,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAC/C,MAAM,EAAE,GAAG,kBAAkB,CAAC,MAAM,CAClC,CAAC,EAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,mBAAU,CAAC,OAAO,CAC1D,CAAC,CAAC,CAAC,CAAC;QAEL,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;QAC5E,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC;IAC5C,CAAC;IAED,IAAI,4BAA4B;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC;IACvD,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAI,WAAW,CAAC,WAAoC;QAClD,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,CAAC;;AA5fD,aAAa;AACG,uBAAc,GAAG,0BAAc,CAAC;AAChD,aAAa;AACG,sBAAa,GAAG,yBAAa,CAAC;AAC9C,aAAa;AACG,mCAA0B,GAAG,sCAA0B,CAAC;AACxE,aAAa;AACG,yBAAgB,GAAG,4BAAgB,CAAC;AACpD,aAAa;AACG,wBAAe,GAAG,2BAAe,CAAC;AAClD,aAAa;AACG,qCAA4B,GAAG,wCAA4B,CAAC;AAC5E,aAAa;AACG,cAAK,GAAG,iBAAK,CAAC;AAC9B,aAAa;AACG,aAAI,GAAG,gBAAI,CAAC;AAC5B,aAAa;AACG,gBAAO,GAAG,mBAAO,CAAC;AAClC,aAAa;AACG,cAAK,GAAG,iBAAK,CAAC;AAC9B,aAAa;AACG,gBAAO,GAAG,mBAAO,CAAC;AAjCvB,4BAAQ;AA2gBrB,2EAA2E;AAC3E,SAAS,aAAa,CACpB,MAAc,EACd,QAAkB,EAClB,OAAwB,EACxB,QAAmB;IAEnB,OAAO,GAAG,OAAO,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IACtC,KAAK,MAAM,KAAK,IAAI,+BAAmB,EAAE;QACvC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;KAClC;IAED,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE;QAC3B,QAAQ,CAAC,IAAI,CACX,QAAQ,CAAC,aAAa,EACtB,IAAI,0BAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CACjE,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,+BAAmB,EAAE;YACvC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAClC;QACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,QAAQ,EAAE,CAAC;SACZ;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,6CAA6C;AAC7C,SAAS,uBAAuB,CAAC,OAAyB;IACxD,IAAI,OAAO,EAAE,gBAAgB,EAAE;QAC7B,OAAO,qBAAY,CAAC,MAAM,CAAC;KAC5B;IAED,IAAI,OAAO,EAAE,UAAU,EAAE;QACvB,OAAO,qBAAY,CAAC,mBAAmB,CAAC;KACzC;IAED,IAAI,OAAO,EAAE,YAAY,EAAE;QACzB,OAAO,qBAAY,CAAC,YAAY,CAAC;KAClC;IAED,OAAO,qBAAY,CAAC,OAAO,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,QAAkB,EAAE,iBAAoC;IACtF,QAAQ,CAAC,IAAI,CACX,QAAQ,CAAC,cAAc,EACvB,IAAI,2BAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,iBAAiB,CAAC,OAAO,CAAC,CACjE,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,QAAQ,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC3E,KAAK,MAAM,KAAK,IAAI,+BAAmB,EAAE;QACvC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAM,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;KACvD;IAED,MAAM,CAAC,EAAE,CAAC,eAAM,CAAC,oBAAoB,EAAE,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;IAEjG,MAAM,CAAC,OAAO,EAAE,CAAC;IACjB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,QAAkB,EAAE,yBAA6C;IACtF,2CAA2C;IAC3C,IAAI,yBAAyB,IAAI,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAAE;QAC1F,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,yBAAyB,CAAC;YACjD,IACE,yBAAyB,CAAC,KAAK,YAAY,kBAAU;gBACrD,yBAAyB,CAAC,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,SAAS,CAAC,EACxE;gBACA,MAAM,yBAAyB,GAAG,yBAAyB,CAAC,KAAK,CAAC,aAAa,CAC7E,uBAAe,CAAC,yBAAyB,CAC1C,CAAC;gBAEF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,yBAAyB,EAAE,CAAC,CAAC;aAClD;iBAAM,IAAI,yBAAyB,CAAC,KAAK,IAAI,IAAI,EAAE;gBAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpD,MAAM,mBAAmB,GACvB,yBAAyB,CAAC,aAAa;oBACvC,CAAC,yBAAyB,CAAC,IAAI,KAAK,mBAAU,CAAC,OAAO;wBACpD,eAAe,KAAK,qBAAY,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,mBAAmB,EAAE;oBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;iBACrB;aACF;SACF;KACF;IAED,6EAA6E;IAC7E,KAAK,MAAM,iBAAiB,IAAI,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE;QACrE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;YACtD,MAAM,MAAM,GAAG,sBAAsB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;YACnE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC3D;KACF;IAED,yFAAyF;IACzF,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE;QACtC,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;YACjD,SAAS;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YAC1C,SAAS;SACV;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACrD,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEzC,wCAAwC;QACxC,IAAI,MAAM,EAAE;YACV,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACjC;KACF;AACH,CAAC;AAED,SAAS,cAAc,CAAC,KAAmC,EAAE,GAAsB;IACjF,OAAO,KAAK,CAAC,MAAM,EAAE;QACnB,MAAM,eAAe,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,eAAe,EAAE;YACpB,SAAS;SACV;QAED,IAAI,eAAe,CAAC,KAAK,EAAE;YACzB,IAAA,qBAAY,EAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;YAChC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SAC/B;KACF;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAkB;IAC1C,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,KAAK,qBAAY,EAAE;QACrC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,gCAAwB,EAAE,CAAC,CAAC;QACrE,OAAO;KACR;IAED,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAY,CAAC,OAAO,CAAC;IACrE,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7E,MAAM,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;IACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,EAAE,CAAC,EAAE;QACzC,MAAM,eAAe,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;QACrD,IAAI,CAAC,eAAe,EAAE;YACpB,SAAS;SACV;QAED,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE;YAC/B,SAAS;SACV;QAED,IAAI,oBAAoB,CAAC;QACzB,IAAI;YACF,MAAM,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;YACtD,oBAAoB,GAAG,cAAc;gBACnC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,kBAAkB,CAAC;gBAC1D,CAAC,CAAC,kBAAkB,CAAC;SACxB;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,eAAe,CAAC,KAAK,EAAE;gBACzB,IAAA,qBAAY,EAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACrC;YAED,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,SAAS;SACV;QAED,IAAI,cAAc,CAAC;QACnB,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3C,SAAS;SACV;aAAM,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SAC1E;aAAM;YACL,MAAM,YAAY,GAAG,IAAA,eAAO,EAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACtD,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAEhE,cAAc;gBACZ,OAAO,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,cAAc;oBACvE,CAAC,CAAC,OAAO;oBACT,CAAC,CAAC,OAAO,CAAC;SACf;QAED,IAAI,CAAC,cAAc,EAAE;YACnB,eAAe,CAAC,QAAQ,CACtB,IAAI,iCAAyB,CAC3B,6FAA6F,EAC7F,QAAQ,CAAC,WAAW,CACrB,CACF,CAAC;YACF,OAAO;SACR;QACD,MAAM,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAChD,IAAI,SAAS,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,IAAI,cAAc,EAAE;YACtE,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;SACvC;QAED,IAAI,eAAe,CAAC,KAAK,EAAE;YACzB,IAAA,qBAAY,EAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACrC;QAED,eAAe,CAAC,QAAQ,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;KACrD;IAED,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QACnC,qDAAqD;QACrD,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE;YAC3C,OAAO,CAAC,QAAQ,CAAC,SAAS,mBAAmB;gBAC3C,OAAO,MAAM,CAAC,YAAY,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,SAAS,wBAAwB,CAC/B,mBAAwC,EACxC,yBAA4C;IAE5C,MAAM,wBAAwB,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAC9D,yBAAyB,CAAC,OAAO,CAClC,CAAC;IACF,MAAM,sBAAsB,GAAG,wBAAwB,EAAE,eAAe,CAAC;IACzE,OAAO,CACL,IAAA,2CAAsB,EAAC,sBAAsB,EAAE,yBAAyB,CAAC,eAAe,CAAC,GAAG,CAAC,CAC9F,CAAC;AACJ,CAAC;AAED,cAAc;AACd,MAAa,kBAAkB;IAI7B,YAAY,KAAe;QACzB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAClC,CAAC;CACF;AA3CD,gDA2CC"}