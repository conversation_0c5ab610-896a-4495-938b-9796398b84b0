const fs = require('fs');
const path = require('path');
const { dbConnection } = require('../database/connection');
const musicBotService = require('../services/musicBotService');

class DataMigration {
  constructor() {
    this.jsonFilePath = path.join(__dirname, '../databases/music.json');
    this.backupPath = path.join(__dirname, '../databases/music_backup.json');
  }

  // قراءة البيانات من ملف JSON
  readJsonData() {
    try {
      if (!fs.existsSync(this.jsonFilePath)) {
        console.log('❌ ملف music.json غير موجود');
        return {};
      }

      const data = fs.readFileSync(this.jsonFilePath, 'utf8');
      const jsonData = JSON.parse(data);
      
      console.log(`✅ تم قراءة ${Object.keys(jsonData).length} سجل من ملف JSON`);
      return jsonData;
      
    } catch (error) {
      console.error('❌ خطأ في قراءة ملف JSON:', error.message);
      return {};
    }
  }

  // إنشاء نسخة احتياطية
  createBackup() {
    try {
      if (fs.existsSync(this.jsonFilePath)) {
        fs.copyFileSync(this.jsonFilePath, this.backupPath);
        console.log('✅ تم إنشاء نسخة احتياطية من البيانات');
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error.message);
    }
  }

  // تنظيف البيانات وتحويلها للصيغة الجديدة
  transformData(jsonData) {
    const transformedData = [];
    
    for (const [botId, data] of Object.entries(jsonData)) {
      try {
        // تنظيف البيانات وتحويلها
        const cleanData = {
          botId: botId,
          volume: this.validateNumber(data.volume, 100, 0, 150),
          repeat: Boolean(data.repeat),
          channelId: this.validateString(data.channelId),
          activity: this.validateActivity(data.activity),
          guildId: this.validateString(data.guildId),
          owners: this.validateArray(data.owners),
          commandsChat: this.validateString(data.commandsChat),
          prefix: this.validateString(data.prefix, "!"),
          color: this.validateString(data.color, "Blue"),
          game: this.validateString(data.game),
          channel: this.validateString(data.channel),
          platform: this.validatePlatform(data.platform)
        };

        transformedData.push(cleanData);
        
      } catch (error) {
        console.error(`❌ خطأ في تحويل البيانات للبوت ${botId}:`, error.message);
      }
    }
    
    console.log(`✅ تم تحويل ${transformedData.length} سجل بنجاح`);
    return transformedData;
  }

  // التحقق من صحة الأرقام
  validateNumber(value, defaultValue = 0, min = null, max = null) {
    const num = Number(value);
    if (isNaN(num)) return defaultValue;
    if (min !== null && num < min) return min;
    if (max !== null && num > max) return max;
    return num;
  }

  // التحقق من صحة النصوص
  validateString(value, defaultValue = null) {
    if (typeof value === 'string' && value.trim() !== '') {
      return value.trim();
    }
    return defaultValue;
  }

  // التحقق من صحة المصفوفات
  validateArray(value) {
    if (Array.isArray(value)) {
      return value.filter(item => typeof item === 'string' && item.trim() !== '');
    }
    return [];
  }

  // التحقق من صحة النشاط
  validateActivity(value) {
    if (Array.isArray(value) && value.length > 0) {
      return value.map(activity => ({
        type: this.validateNumber(activity.type, 0),
        name: this.validateString(activity.name)
      }));
    } else if (typeof value === 'object' && value !== null) {
      return [{
        type: this.validateNumber(value.type, 0),
        name: this.validateString(value.name)
      }];
    }
    return [{ type: 0, name: null }];
  }

  // التحقق من صحة المنصة
  validatePlatform(value) {
    const validPlatforms = ['youtube', 'spotify', 'soundcloud', 'deezer'];
    if (typeof value === 'string' && validPlatforms.includes(value.toLowerCase())) {
      return value.toLowerCase();
    }
    return 'youtube';
  }

  // هجرة البيانات إلى MongoDB
  async migrateToMongoDB(transformedData) {
    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    console.log(`🔄 بدء هجرة ${transformedData.length} سجل إلى MongoDB...`);

    for (const botData of transformedData) {
      try {
        await musicBotService.set(botData.botId, botData);
        successCount++;
        
        if (successCount % 10 === 0) {
          console.log(`✅ تم هجرة ${successCount} سجل...`);
        }
        
      } catch (error) {
        errorCount++;
        errors.push({
          botId: botData.botId,
          error: error.message
        });
        console.error(`❌ خطأ في هجرة البوت ${botData.botId}:`, error.message);
      }
    }

    return {
      successCount,
      errorCount,
      errors
    };
  }

  // التحقق من صحة الهجرة
  async verifyMigration(originalData) {
    console.log('🔍 التحقق من صحة الهجرة...');
    
    const originalCount = Object.keys(originalData).length;
    const stats = await musicBotService.getStats();
    
    console.log(`📊 البيانات الأصلية: ${originalCount} سجل`);
    console.log(`📊 البيانات في MongoDB: ${stats.totalBots} سجل`);
    
    if (originalCount === stats.totalBots) {
      console.log('✅ تم التحقق من صحة الهجرة بنجاح');
      return true;
    } else {
      console.log('⚠️ عدد السجلات غير متطابق');
      return false;
    }
  }

  // تشغيل عملية الهجرة الكاملة
  async run() {
    try {
      console.log('🚀 بدء عملية هجرة البيانات...');
      
      // الاتصال بقاعدة البيانات
      await dbConnection.connect();
      
      // إنشاء نسخة احتياطية
      this.createBackup();
      
      // قراءة البيانات الأصلية
      const originalData = this.readJsonData();
      
      if (Object.keys(originalData).length === 0) {
        console.log('⚠️ لا توجد بيانات للهجرة');
        return;
      }
      
      // تحويل البيانات
      const transformedData = this.transformData(originalData);
      
      // هجرة البيانات
      const result = await this.migrateToMongoDB(transformedData);
      
      // التحقق من صحة الهجرة
      const isValid = await verifyMigration(originalData);
      
      // تقرير النتائج
      console.log('\n📋 تقرير الهجرة:');
      console.log(`✅ تم هجرة ${result.successCount} سجل بنجاح`);
      console.log(`❌ فشل في هجرة ${result.errorCount} سجل`);
      console.log(`🔍 صحة الهجرة: ${isValid ? 'مؤكدة' : 'غير مؤكدة'}`);
      
      if (result.errors.length > 0) {
        console.log('\n❌ الأخطاء:');
        result.errors.forEach(error => {
          console.log(`  - ${error.botId}: ${error.error}`);
        });
      }
      
      console.log('\n✅ انتهت عملية الهجرة');
      
    } catch (error) {
      console.error('❌ خطأ في عملية الهجرة:', error.message);
    } finally {
      await dbConnection.disconnect();
    }
  }
}

// تشغيل الهجرة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  const migration = new DataMigration();
  migration.run().catch(console.error);
}

module.exports = DataMigration;
