# تحويل نظام تخزين بيانات بوت الموسيقى إلى MongoDB

## 📋 **نظرة عامة**

تم تحويل نظام تخزين بيانات بوت الموسيقى من ملفات JSON (st.db) إلى قاعدة بيانات MongoDB لتحسين الأداء والموثوقية.

## 🏗️ **البنية الجديدة**

### **الملفات المضافة:**

1. **`models/MusicBot.js`** - نموذج MongoDB للبيانات
2. **`database/connection.js`** - إدارة الاتصال بقاعدة البيانات
3. **`services/musicBotService.js`** - طبقة الخدمات للعمليات CRUD
4. **`scripts/migrate.js`** - سكريبت هجرة البيانات
5. **`.env.example`** - مثال على متغيرات البيئة

### **التحديثات:**

- **`package.json`** - إضافة mongoose و dotenv
- **`index.js`** - استبدال st.db بـ MongoDB

## 🔧 **الإعداد**

### **1. تثبيت المكتبات الجديدة:**

```bash
cd projects/music
npm install mongoose dotenv
```

### **2. إعداد متغيرات البيئة:**

```bash
# نسخ ملف المثال
cp .env.example .env

# تحرير الملف وإضافة رابط MongoDB
nano .env
```

### **3. إعداد MongoDB:**

#### **خيار 1: MongoDB محلي**
```bash
# تثبيت MongoDB محلياً
# Windows: تحميل من mongodb.com
# Linux: sudo apt install mongodb

# تشغيل الخدمة
sudo systemctl start mongodb
```

#### **خيار 2: MongoDB Atlas (السحابي)**
1. إنشاء حساب على [MongoDB Atlas](https://www.mongodb.com/atlas)
2. إنشاء cluster جديد
3. الحصول على connection string
4. إضافة الرابط في `.env`:

```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/music_bots?retryWrites=true&w=majority
```

## 🔄 **هجرة البيانات**

### **تشغيل سكريبت الهجرة:**

```bash
cd projects/music
node scripts/migrate.js
```

### **ما يحدث أثناء الهجرة:**

1. **إنشاء نسخة احتياطية** من `music.json`
2. **قراءة البيانات** الحالية من JSON
3. **تنظيف وتحويل** البيانات للصيغة الجديدة
4. **رفع البيانات** إلى MongoDB
5. **التحقق** من صحة الهجرة

## 📊 **نموذج البيانات الجديد**

```javascript
{
  botId: String,           // معرف البوت (فريد)
  volume: Number,          // مستوى الصوت (0-150)
  repeat: Boolean,         // وضع التكرار
  channelId: String,       // معرف القناة الصوتية
  activity: Mixed,         // نشاط البوت
  guildId: String,         // معرف السيرفر
  owners: [String],        // قائمة المالكين
  commandsChat: String,    // شات الأوامر المحدد
  prefix: String,          // البادئة
  color: String,           // اللون
  game: String,            // اللعبة/النشاط
  channel: String,         // القناة
  platform: String,       // المنصة المفضلة
  createdAt: Date,         // تاريخ الإنشاء
  updatedAt: Date          // تاريخ آخر تحديث
}
```

## 🔍 **المميزات الجديدة**

### **1. أداء محسن:**
- استعلامات أسرع مع الفهارس
- دعم العمليات المتزامنة
- تحسين استخدام الذاكرة

### **2. موثوقية أعلى:**
- نسخ احتياطية تلقائية
- استرداد من الأخطاء
- تحقق من صحة البيانات

### **3. قابلية التوسع:**
- دعم عدد أكبر من البوتات
- توزيع الحمولة
- مراقبة الأداء

## 🛠️ **العمليات الجديدة**

### **الحصول على بيانات بوت:**
```javascript
const data = await musicBotService.get(botId);
```

### **حفظ/تحديث البيانات:**
```javascript
await musicBotService.set(botId, data);
```

### **إضافة مالك:**
```javascript
await musicBotService.addOwner(botId, ownerId);
```

### **البحث عن البوتات:**
```javascript
const bots = await musicBotService.find({ guildId: "123456789" });
```

## 📈 **المراقبة والإحصائيات**

### **الحصول على إحصائيات:**
```javascript
const stats = await musicBotService.getStats();
console.log(stats);
// {
//   totalBots: 150,
//   activeBots: 120,
//   botsWithOwners: 100,
//   inactiveBots: 30
// }
```

### **فحص الاتصال:**
```javascript
const isConnected = dbConnection.isConnectedToDatabase();
await dbConnection.testConnection();
```

## 🔧 **استكشاف الأخطاء**

### **مشاكل الاتصال:**
```bash
# فحص حالة MongoDB
sudo systemctl status mongodb

# فحص اللوجات
tail -f /var/log/mongodb/mongod.log
```

### **مشاكل الهجرة:**
```bash
# تشغيل الهجرة مع تفاصيل أكثر
DEBUG=* node scripts/migrate.js

# استعادة النسخة الاحتياطية
cp databases/music_backup.json databases/music.json
```

## 🔄 **العودة للنظام القديم (إذا لزم الأمر)**

1. **إيقاف البوتات**
2. **استعادة النسخة الاحتياطية:**
   ```bash
   cp databases/music_backup.json databases/music.json
   ```
3. **التراجع عن التغييرات في index.js**
4. **إزالة المكتبات الجديدة:**
   ```bash
   npm uninstall mongoose dotenv
   ```

## 📞 **الدعم**

في حالة وجود مشاكل:

1. **فحص اللوجات** في وحدة التحكم
2. **التأكد من إعدادات** متغيرات البيئة
3. **فحص الاتصال** بقاعدة البيانات
4. **مراجعة الوثائق** أعلاه

## ✅ **قائمة التحقق**

- [ ] تثبيت MongoDB
- [ ] إعداد متغيرات البيئة
- [ ] تشغيل سكريبت الهجرة
- [ ] فحص البيانات في MongoDB
- [ ] اختبار البوتات
- [ ] مراقبة الأداء

---

**ملاحظة:** هذا التحويل يحسن من أداء وموثوقية النظام بشكل كبير، ويوفر أساساً قوياً للتطوير المستقبلي.
