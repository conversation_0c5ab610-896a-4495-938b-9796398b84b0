console.log('🔍 فحص تحميل متغيرات البيئة...\n');

// فحص مسار العمل
console.log('📁 مسار العمل الحالي:', process.cwd());

// فحص وجود ملف .env
const fs = require('fs');
const path = require('path');

const envPath = path.join(process.cwd(), '.env');
console.log('📄 مسار ملف .env:', envPath);
console.log('📄 ملف .env موجود:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  console.log('📄 محتوى ملف .env:');
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log(envContent);
} else {
  console.log('❌ ملف .env غير موجود!');
}

// تحميل متغيرات البيئة
console.log('\n🔄 تحميل متغيرات البيئة...');
require('dotenv').config();

// فحص المتغيرات
console.log('\n📊 متغيرات البيئة:');
console.log('MONGODB_URI:', process.env.MONGODB_URI ? 'موجود' : 'غير موجود');

if (process.env.MONGODB_URI) {
  const hiddenUri = process.env.MONGODB_URI.replace(/\/\/.*@/, '//***:***@');
  console.log('رابط MongoDB:', hiddenUri);
} else {
  console.log('❌ متغير MONGODB_URI غير موجود');
  console.log('🔍 جميع متغيرات البيئة المتاحة:');
  Object.keys(process.env).filter(key => key.includes('MONGO')).forEach(key => {
    console.log(`  ${key}: ${process.env[key]}`);
  });
}

// اختبار تحميل ملف الاتصال
console.log('\n🧪 اختبار تحميل ملف الاتصال...');
try {
  const { dbConnection } = require('./database/connection');
  console.log('✅ تم تحميل ملف الاتصال بنجاح');
  
  console.log('📊 معلومات الاتصال:');
  console.log('- رابط الاتصال:', dbConnection.connectionString ? 'محدد' : 'غير محدد');
  
} catch (error) {
  console.error('❌ خطأ في تحميل ملف الاتصال:', error.message);
}
