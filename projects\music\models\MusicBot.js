const mongoose = require('mongoose');

// نموذج بيانات بوت الموسيقى
const musicBotSchema = new mongoose.Schema({
  // معرف البوت (يمكن أن يكون ID أو Token)
  botId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // مستوى الصوت (0-150)
  volume: {
    type: Number,
    default: 100,
    min: 0,
    max: 150
  },
  
  // وضع التكرار
  repeat: {
    type: Boolean,
    default: false
  },
  
  // معرف القناة الصوتية الحالية
  channelId: {
    type: String,
    default: null
  },
  
  // نشاط/حالة البوت
  activity: {
    type: mongoose.Schema.Types.Mixed,
    default: [{ type: 0, name: null }]
  },
  
  // معرف السيرفر
  guildId: {
    type: String,
    default: null
  },
  
  // قائمة المالكين
  owners: {
    type: [String],
    default: []
  },
  
  // شات الأوامر المحدد
  commandsChat: {
    type: String,
    default: null
  },
  
  // البادئة (prefix)
  prefix: {
    type: String,
    default: "!"
  },
  
  // اللون
  color: {
    type: String,
    default: "Blue"
  },
  
  // اللعبة/النشاط
  game: {
    type: String,
    default: null
  },
  
  // القناة
  channel: {
    type: String,
    default: null
  },
  
  // المنصة المفضلة للموسيقى
  platform: {
    type: String,
    enum: ['youtube', 'spotify', 'soundcloud', 'deezer'],
    default: 'youtube'
  }
}, {
  // إضافة timestamps تلقائياً
  timestamps: true,
  
  // تحسين الاستعلامات
  collection: 'musicbots'
});

// إنشاء فهارس للبحث السريع
musicBotSchema.index({ botId: 1 });
musicBotSchema.index({ guildId: 1 });
musicBotSchema.index({ owners: 1 });
musicBotSchema.index({ commandsChat: 1 });

// دالة للحصول على البيانات الافتراضية
musicBotSchema.statics.getDefaultData = function() {
  return {
    volume: 100,
    repeat: false,
    channelId: null,
    activity: [{ type: 0, name: null }],
    guildId: null,
    owners: [],
    commandsChat: null,
    prefix: "!",
    color: "Blue",
    game: null,
    channel: null,
    platform: 'youtube'
  };
};

// دالة للبحث أو الإنشاء
musicBotSchema.statics.findOrCreate = async function(botId, defaultData = {}) {
  let bot = await this.findOne({ botId });
  
  if (!bot) {
    const data = { ...this.getDefaultData(), ...defaultData, botId };
    bot = new this(data);
    await bot.save();
  }
  
  return bot;
};

// دالة لتحديث البيانات
musicBotSchema.methods.updateData = async function(updateData) {
  Object.assign(this, updateData);
  return await this.save();
};

// دالة لإضافة مالك
musicBotSchema.methods.addOwner = async function(ownerId) {
  if (!this.owners.includes(ownerId)) {
    this.owners.push(ownerId);
    return await this.save();
  }
  return this;
};

// دالة لإزالة مالك
musicBotSchema.methods.removeOwner = async function(ownerId) {
  this.owners = this.owners.filter(id => id !== ownerId);
  return await this.save();
};

// دالة للتحقق من المالكية
musicBotSchema.methods.isOwner = function(userId) {
  return this.owners.includes(userId);
};

// تصدير النموذج
module.exports = mongoose.model('MusicBot', musicBotSchema);
