{"version": 3, "file": "message_stream.js", "sourceRoot": "", "sources": ["../../src/cmap/message_stream.ts"], "names": [], "mappings": ";;;AAAA,mCAAoD;AAGpD,oCAAoE;AAEpE,oCAAqD;AACrD,yCAMoB;AACpB,6DAMqC;AACrC,yDAAkE;AAElE,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAC/B,MAAM,wBAAwB,GAAG,CAAC,CAAC,CAAC,kDAAkD;AAEtF,MAAM,0BAA0B,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,gBAAgB;AAChB,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAqBjC;;;;GAIG;AACH,MAAa,aAAc,SAAQ,eAAM;IAQvC,YAAY,UAAgC,EAAE;QAC5C,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,gBAAgB;QAChB,2BAAsB,GAAG,KAAK,CAAC;QAI7B,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,0BAA0B,CAAC;QACnF,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,kBAAU,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAEQ,MAAM,CAAC,KAAa,EAAE,CAAU,EAAE,QAA0B;QACnE,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEQ,KAAK,EAAC,UAAU;QACvB,gFAAgF;QAChF,uCAAuC;QACvC,OAAO;IACT,CAAC;IAED,YAAY,CACV,OAAiC,EACjC,oBAA0C;QAE1C,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,IAAI,MAAM,CAAC;QACzE,IAAI,gBAAgB,KAAK,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;YACxD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,kCAAkC;QAClC,MAAM,iCAAiC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QACzE,MAAM,qBAAqB,GAAG,iCAAiC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAE3F,6EAA6E;QAC7E,MAAM,qBAAqB,GAAG,iCAAiC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAEhF,MAAM,OAAO,GAAG;YACd,gBAAgB;YAChB,oBAAoB,EAAE,oBAAoB,CAAC,oBAAoB,IAAI,CAAC;SACrE,CAAC;QACF,4BAA4B;QAC5B,IAAA,sBAAQ,EAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC,IAAI,CAC3C,iBAAiB,CAAC,EAAE;YAClB,wCAAwC;YACxC,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACpD,SAAS,CAAC,YAAY,CACpB,mBAAmB,GAAG,wBAAwB,GAAG,iBAAiB,CAAC,MAAM,EACzE,CAAC,CACF,CAAC,CAAC,gBAAgB;YACnB,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY;YAC1D,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,oBAAoB;YAClD,SAAS,CAAC,YAAY,CAAC,yBAAa,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS;YAEpD,kDAAkD;YAClD,MAAM,kBAAkB,GAAG,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAClE,kBAAkB,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB;YAC5E,kBAAkB,CAAC,YAAY,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,sEAAsE;YACxI,kBAAkB,CAAC,UAAU,CAAC,wBAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,eAAe;YAC/E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC,EACD,KAAK,CAAC,EAAE;YACN,oBAAoB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AA3ED,sCA2EC;AAED,mEAAmE;AACnE,uEAAuE;AACvE,SAAS,WAAW,CAAC,OAAiC;IACpD,MAAM,UAAU,GAAG,OAAO,YAAY,cAAG,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5E,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAO,CAAC,oCAAsB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAqB,EAAE,QAA0B;IAC5E,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/B,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IAExC,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,OAAO,QAAQ,EAAE,CAAC;KACnB;IAED,IAAI,aAAa,GAAG,CAAC,EAAE;QACrB,OAAO,QAAQ,CAAC,IAAI,uBAAe,CAAC,yBAAyB,aAAa,EAAE,CAAC,CAAC,CAAC;KAChF;IAED,IAAI,aAAa,GAAG,MAAM,CAAC,kBAAkB,EAAE;QAC7C,OAAO,QAAQ,CACb,IAAI,uBAAe,CACjB,yBAAyB,aAAa,kBAAkB,MAAM,CAAC,kBAAkB,EAAE,CACpF,CACF,CAAC;KACH;IAED,IAAI,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE;QACjC,OAAO,QAAQ,EAAE,CAAC;KACnB;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3C,MAAM,aAAa,GAAkB;QACnC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9B,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QACjC,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QAClC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;KAChC,CAAC;IAEF,MAAM,sBAAsB,GAAG,GAAG,EAAE;QAClC,IAAI,MAAM,CAAC,sBAAsB,EAAE;YACjC,qCAAqC;YACrC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YACxC,IAAI,aAAa,IAAI,IAAI,IAAI,aAAa,IAAI,MAAM,CAAC,MAAM,EAAE;gBAC3D,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,IAAI,YAAY,GAAG,aAAa,CAAC,MAAM,KAAK,kBAAM,CAAC,CAAC,CAAC,iBAAM,CAAC,CAAC,CAAC,mBAAQ,CAAC;IACvE,IAAI,aAAa,CAAC,MAAM,KAAK,yBAAa,EAAE;QAC1C,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;QAE1D,uDAAuD;QACvD,yEAAyE;QACzE,8DAA8D;QAC9D,IAAI,sBAAsB,EAAE,EAAE;YAC5B,OAAO,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;QAED,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;QAE9E,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;YACtB,OAAO,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;QACD,OAAO,QAAQ,EAAE,CAAC;KACnB;IAED,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC;IACpC,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAChE,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;IACpE,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;IACtD,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;IAEhE,sCAAsC;IACtC,YAAY,GAAG,aAAa,CAAC,MAAM,KAAK,kBAAM,CAAC,CAAC,CAAC,iBAAM,CAAC,CAAC,CAAC,mBAAQ,CAAC;IACnE,IAAA,wBAAU,EAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAC7C,WAAW,CAAC,EAAE;QACZ,IAAI,WAAW,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE;YAC/C,OAAO,QAAQ,CACb,IAAI,+BAAuB,CAAC,yDAAyD,CAAC,CACvF,CAAC;SACH;QAED,uDAAuD;QACvD,yEAAyE;QACzE,8DAA8D;QAC9D,IAAI,sBAAsB,EAAE,EAAE;YAC5B,OAAO,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;QACD,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;QAE9E,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;YACtB,OAAO,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;QACD,OAAO,QAAQ,EAAE,CAAC;IACpB,CAAC,EACD,KAAK,CAAC,EAAE;QACN,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CACF,CAAC;AACJ,CAAC"}