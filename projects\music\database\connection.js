const mongoose = require('mongoose');

class DatabaseConnection {
  constructor() {
    this.isConnected = false;

    // التحقق من وجود رابط MongoDB
    if (!process.env.MONGODB_URI) {
      throw new Error('MONGODB_URI environment variable is required. Please set it in your .env file.');
    }

    this.connectionString = process.env.MONGODB_URI;
    this.options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10, // الحد الأقصى لعدد الاتصالات في المجموعة
      serverSelectionTimeoutMS: 5000, // مهلة اختيار الخادم
      socketTimeoutMS: 45000, // مهلة المقبس
      family: 4, // استخدام IPv4
      retryWrites: true,
      w: 'majority'
    };
  }

  // الاتصال بقاعدة البيانات
  async connect() {
    try {
      if (this.isConnected) {
        console.log('Already connected to MongoDB');
        return;
      }

      console.log('Connecting to MongoDB...');
      console.log('Connection string:', this.connectionString.replace(/\/\/.*@/, '//***:***@')); // إخفاء كلمة المرور في اللوج

      await mongoose.connect(this.connectionString, this.options);

      this.isConnected = true;
      console.log('✅ Successfully connected to MongoDB');

      // معالجة أحداث الاتصال
      this.setupEventHandlers();

    } catch (error) {
      console.error('❌ Error connecting to MongoDB:', error.message);
      throw error;
    }
  }

  // قطع الاتصال
  async disconnect() {
    try {
      if (!this.isConnected) {
        console.log('Already disconnected from MongoDB');
        return;
      }

      await mongoose.disconnect();
      this.isConnected = false;
      console.log('✅ Successfully disconnected from MongoDB');

    } catch (error) {
      console.error('❌ Error disconnecting from MongoDB:', error.message);
      throw error;
    }
  }

  // إعداد معالجات الأحداث
  setupEventHandlers() {
    // عند فقدان الاتصال
    mongoose.connection.on('disconnected', () => {
      console.log('⚠️ MongoDB disconnected');
      this.isConnected = false;
    });

    // عند حدوث خطأ
    mongoose.connection.on('error', (error) => {
      console.error('❌ MongoDB connection error:', error.message);
      this.isConnected = false;
    });

    // عند إعادة الاتصال
    mongoose.connection.on('reconnected', () => {
      console.log('✅ MongoDB reconnected');
      this.isConnected = true;
    });

    // عند إغلاق التطبيق
    process.on('SIGINT', async () => {
      console.log('\n🔄 Gracefully shutting down...');
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🔄 Gracefully shutting down...');
      await this.disconnect();
      process.exit(0);
    });
  }

  // التحقق من حالة الاتصال
  isConnectedToDatabase() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  // الحصول على معلومات الاتصال
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  // اختبار الاتصال
  async testConnection() {
    try {
      if (!this.isConnectedToDatabase()) {
        throw new Error('Not connected to database');
      }

      // تنفيذ استعلام بسيط للاختبار
      await mongoose.connection.db.admin().ping();
      console.log('✅ Database connection test successful');
      return true;

    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
      return false;
    }
  }

  // إعادة الاتصال
  async reconnect() {
    try {
      console.log('🔄 Attempting to reconnect to MongoDB...');
      await this.disconnect();
      await this.connect();
      return true;

    } catch (error) {
      console.error('❌ Failed to reconnect to MongoDB:', error.message);
      return false;
    }
  }
}

// إنشاء مثيل واحد للاتصال
const dbConnection = new DatabaseConnection();

// تصدير المثيل والكلاس
module.exports = {
  dbConnection,
  DatabaseConnection
};
