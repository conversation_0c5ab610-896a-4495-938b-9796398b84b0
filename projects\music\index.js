const pm2 = require("pm2");
const Discord = require("discord.js");
const { MessageFlags } = require("discord.js");
const { GatewayIntentBits, Partials, ActivityType, EmbedBuilder } = require("discord.js");
const { joinVoiceChannel } = require("@discordjs/voice");


const config = {
  owners: process.env.owner_id ? [process.env.owner_id] : []
};
const { DisTube } = require("distube");
const { DeezerPlugin } = require("@distube/deezer");
const { SpotifyPlugin } = require("@distube/spotify");
const { SoundCloudPlugin } = require("@distube/soundcloud");
const { Database } = require("st.db");
const music_db = new Database("projects/music/databases/music.json");
const fs = require('fs');

// التأكد من وجود مجلد databases
if (!fs.existsSync("projects/music/databases")) {
  fs.mkdirSync("projects/music/databases", { recursive: true });
}

// التأكد من وجود ملف music.json
if (!fs.existsSync("projects/music/databases/music.json")) {
  fs.writeFileSync("projects/music/databases/music.json", JSON.stringify({}, null, 2));
}
const axios = require("axios");
const sub_db = new Database("databases/subscriptions.json");

const defaultData = {
  volume: 100,
  repeat: false,
  channelId: null,
  activity: [{ type: ActivityType.Playing, name: null }],
  guildId: null,
  owners: [],
  commandsChat: null,
}

// دالة مساعدة لتحديث ملف music.json
async function updateMusicJson(botId, data, description = "") {
  try {
    // تحديث قاعدة البيانات في الذاكرة
    await music_db.set(botId, data);

    // تحديث الملف مباشرة
    const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
    musicData[botId] = data;
    fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));

    if (description) {
      console.log(`Updated music.json for bot ${botId} - ${description}`);
    }

    return true;
  } catch (error) {
    console.error(`Error updating music.json:`, error);
    return false;
  }
}
async function runBots() {
  try {
    // التحقق من وجود معرف المالك
    if (!process.env.owner_id) {
      console.error("Error: owner_id environment variable is not set");
      return process.exit(1);
    }

    // الحصول على بيانات الاشتراكات
    let sub_data = await sub_db.get(process.env.owner_id);
    if (!sub_data || !Array.isArray(sub_data) || sub_data.length === 0) {
      console.error(`No subscription data found for owner ID: ${process.env.owner_id}`);
      return process.exit(1);
    }

    // التحقق من وجود معرف الاشتراك
    if (!process.env.subscription_id) {
      console.error("Error: subscription_id environment variable is not set");
      return process.exit(1);
    }

    // البحث عن الاشتراك المطلوب
    let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
    if (!subscription) {
      console.error(`No subscription found with ID: ${process.env.subscription_id}`);
      return process.exit(1);
    }

    // تشغيل البوتات
    subscription.bots.forEach((token) => {
      runMusic(token.botToken);
    });
  } catch (error) {
    console.error("Error in runBots:", error);
    return process.exit(1);
  }
}
runBots();

function runMusic(token) {
  const bot_messages = new Map();
  const music_client = new Discord.Client({
    partials: [
      Partials.User,
      Partials.GuildMember,
      Partials.Channel,
      Partials.Message
    ],
    intents: [
      GatewayIntentBits.Guilds,
      GatewayIntentBits.GuildMembers,
      GatewayIntentBits.GuildMessages,
      GatewayIntentBits.MessageContent,
      GatewayIntentBits.GuildVoiceStates
    ]
  });
  let channelId = null;
  music_client.distube = new DisTube(music_client, {
    leaveOnStop: false,
    leaveOnFinish: false,
    emitNewSongOnly: true,
    leaveOnEmpty: false,
    nsfw: false,
    emitAddSongWhenCreatingQueue: true,
    emitAddListWhenCreatingQueue: true,
    searchSongs: 1,
    // إعدادات ytdl محسنة
    ytdlOptions: {
      quality: 'highestaudio',
      highWaterMark: 1 << 25,
      filter: 'audioonly',
      requestOptions: {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      }
    },
    plugins: [
      new DeezerPlugin({
        emitEventsAfterFetching: true
      }),
      new SpotifyPlugin({
        emitEventsAfterFetching: true
      }),
      new SoundCloudPlugin()
    ]
  });



  music_client.on("ready", async () => {
    try {
      console.log("Login Successfully:", music_client.user.tag);

      // تنظيف bot_messages عند إعادة تشغيل البوت
      bot_messages.clear();
      console.log(`Cleared bot_messages cache for ${music_client.user.tag}`);

      // التحقق من وجود معرف المالك
      if (!process.env.owner_id) {
        console.error("Error: owner_id environment variable is not set");
        process.env.owner_id = "default_owner_id"; // استخدام قيمة افتراضية
        console.log("Using default owner_id:", process.env.owner_id);
      }

      // الحصول على بيانات الاشتراكات
      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data) || sub_data.length === 0) {
        console.error(`No subscription data found for owner ID: ${process.env.owner_id}`);
        // إنشاء بيانات افتراضية
        sub_data = [{ id: "default_subscription", guildID: music_client.guilds.cache.first()?.id }];
        console.log("Using default subscription data:", sub_data);
      }

      // التحقق من وجود معرف الاشتراك
      if (!process.env.subscription_id) {
        console.error("Error: subscription_id environment variable is not set");
        process.env.subscription_id = sub_data[0].id; // استخدام أول اشتراك متاح
        console.log("Using default subscription_id:", process.env.subscription_id);
      }

      // البحث عن الاشتراك المطلوب
      let subscription = sub_data.find(s => s.id == process.env.subscription_id);
      if (!subscription) {
        console.error(`No subscription found with ID: ${process.env.subscription_id}`);
        return process.exit(1);
      }

      // تعيين معرف السيرفر
      process.env.guild_id = subscription.guildID;

      // مغادرة السيرفرات الأخرى
      music_client.guilds.cache.filter(g => g.id != process.env.guild_id).forEach(guild => guild.leave());

      // الحصول على بيانات البوت
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      channelId = data.commandsChat;

      // الانضمام إلى القناة الصوتية
      let channel = music_client.channels.cache.get(data.channelId);
      if (channel) {
        try {
          music_client.distube.voices.join(channel);
        } catch (error) {
          console.error("Error joining voice channel:", error);
        }
      }

      // تعيين حالة البوت
      if (data.game) {
        try {
          music_client.user.setPresence({
            activities: [{
              name: data.game,
              type: 0 // PLAYING
            }],
            status: 'online'
          });
          console.log(`Set presence for ${music_client.user.username}: ${data.game}`);
        } catch (error) {
          console.error("Error setting presence:", error);
        }
      } else if (data.activity?.name) {
        try {
          music_client.user.setPresence({
            activities: [data.activity]
          });
        } catch (error) {
          console.error("Error setting presence:", error);
        }
      }
    } catch (error) {
      console.error("Error in ready event:", error);
    }
  });

  music_client.on("guildCreate", async guild => {
    if (process.env.guild_id != guild.id) return guild.leave();
  });

  music_client.on("messageDelete", async message => {
    let msg = bot_messages.get(message.id);
    if (!msg) return;
    try {
      msg.delete().catch(error => console.error("Error deleting message:", error));
    } catch (error) {
      console.error("Error in messageDelete event:", error);
    }
  });

  // معالج الأزرار التفاعلية
  music_client.on("interactionCreate", async (interaction) => {
    if (!interaction.isButton()) return;

    // استثناء أزرار إعادة التشغيل من التحقق من وجود قائمة تشغيل
    if (interaction.customId === "confirm_restart" || interaction.customId === "cancel_restart") {
      // هذه الأزرار لها معالجات خاصة بها في مكان آخر
      return;
    }

    // الحصول على قائمة التشغيل الحالية
    const queue = music_client.distube.getQueue(interaction.guild);
    if (!queue) return interaction.reply({
      content: `:no_entry_sign: There must be music playing to use that!`,
      flags: Discord.MessageFlags.Ephemeral
    });

    // التحقق من أن المستخدم في نفس الروم الصوتي
    if (!interaction.member.voice.channelId) {
      return interaction.reply({
        content: `:no_entry_sign: You must join a voice channel to use that!`,
        flags: Discord.MessageFlags.Ephemeral
      });
    }

    const client_m = interaction.guild.members.me;
    if (client_m.voice.channelId && interaction.member.voice.channelId !== client_m.voice.channelId) {
      return interaction.reply({
        content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!`,
        flags: Discord.MessageFlags.Ephemeral
      });
    }

    // الحصول على بيانات البوت
    let data = await music_db.get(music_client.user.id);
    if (!data) {
      data = defaultData;
    }

    // معالجة الأزرار المختلفة
    switch (interaction.customId) {
      case "repeat":
        // تبديل وضع التكرار
        let repeat = data.repeat ? false : true;
        data.repeat = repeat;

        // تحديث قاعدة البيانات باستخدام الدالة المساعدة
        await updateMusicJson(music_client.user.id, data, "Set repeat mode");

        try {
          queue.setRepeatMode(repeat ? 1 : 0);
        } catch { }

        await interaction.reply({
          content: `> 🎶 Repeat Mode: **${data.repeat ? "ON" : "OFF"}**`,
          flags: Discord.MessageFlags.Ephemeral
        });
        break;

      case "volume_down":
        // خفض مستوى الصوت بمقدار 10
        let newVolDown = Math.max(0, data.volume - 10);
        let oldVolDown = data.volume;
        data.volume = newVolDown;

        // تحديث قاعدة البيانات في الذاكرة
        await music_db.set(music_client.user.id, data);

        // تحديث الملف مباشرة
        try {
          const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
          musicData[music_client.user.id] = data;
          fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        } catch (error) {
          console.error(`Error updating music.json:`, error);
        }

        // تغيير مستوى الصوت بشكل تدريجي
        await changeVolumeGradually(queue, oldVolDown, newVolDown);

        const embedVolDown = new Discord.EmbedBuilder()
          .setColor("#36393f")
          .setTitle("Volume Level")
          .setDescription(`Current volume is: ${newVolDown}`)
        await interaction.reply({ embeds: [embedVolDown], flags: Discord.MessageFlags.Ephemeral });
        break;

      case "pause_resume":
        // تبديل حالة التشغيل/الإيقاف المؤقت
        if (queue.paused) {
          try {
            queue.resume();
          } catch { }
          await interaction.reply({
            content: `> :notes: Resumed **${queue.songs[0].name}**.`,
            flags: Discord.MessageFlags.Ephemeral
          });
        } else {
          try {
            queue.pause();
          } catch { }
          await interaction.reply({
            content: `> :notes: Paused **${queue.songs[0].name}**. Click again to resume!`,
            flags: Discord.MessageFlags.Ephemeral
          });
        }
        break;

      case "volume_up":
        // رفع مستوى الصوت بمقدار 10
        let newVolUp = Math.min(150, data.volume + 10);
        let oldVolUp = data.volume;
        data.volume = newVolUp;

        // تحديث قاعدة البيانات في الذاكرة
        await music_db.set(music_client.user.id, data);

        // تحديث الملف مباشرة
        try {
          const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
          musicData[music_client.user.id] = data;
          fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        } catch (error) {
          console.error(`Error updating music.json:`, error);
        }

        // تغيير مستوى الصوت بشكل تدريجي
        await changeVolumeGradually(queue, oldVolUp, newVolUp);

        const embedVolUp = new Discord.EmbedBuilder()
          .setColor("#36393f")
          .setTitle("Volume Level")
          .setDescription(`Current volume is: ${newVolUp}`)
        await interaction.reply({ embeds: [embedVolUp], flags: Discord.MessageFlags.Ephemeral });
        break;

      case "skip":
        // تخطي الأغنية الحالية
        try {
          if (queue.songs.length > 1) {
            queue.skip();
          } else {
            queue.stop();
          }
        } catch { }

        const embedSkip = new Discord.EmbedBuilder()
          .setColor("#36393f")
          .setTitle("Skip")
          .setDescription(`Skipped ${queue.songs[0].name} - Added by: @${interaction.user.username}`)
        await interaction.reply({ embeds: [embedSkip], flags: Discord.MessageFlags.Ephemeral });
        break;

      default:
        await interaction.reply({
          content: "Unknown button action",
          flags: Discord.MessageFlags.Ephemeral
        });
    }
  });

  music_client.on("messageCreate", async message => {
    if (!message.guild || message.author.bot) return;

    // إعادة تحميل بيانات البوت في كل رسالة للتأكد من التحديثات
    let data = await music_db.get(music_client.user.id);
    if (!data) {
      data = defaultData;
    }
    // تحديث channelId من قاعدة البيانات
    channelId = data.commandsChat;

    let args = message.content.split(" ");
    // أمر settings يعمل فقط عند منشن البوت
    if (message.mentions.users.has(music_client.user.id) && args.includes("settings")) {
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;

      // إنشاء قائمة الإعدادات
      const settingsMenu = new Discord.StringSelectMenuBuilder()
        .setCustomId('settings_menu')
        .setPlaceholder('اختر إعداداً للتحكم به')
        .addOptions([
          {
            label: 'إضافة مالك',
            description: 'إضافة مستخدم جديد كمالك للبوت',
            value: 'add_owner',
            emoji: '➕'
          },
          {
            label: 'إزالة مالك',
            description: 'إزالة مستخدم من قائمة المالكين',
            value: 'remove_owner',
            emoji: '➖'
          },
          {
            label: 'قائمة المالكين',
            description: 'عرض جميع المالكين الحاليين',
            value: 'list_owners',
            emoji: '📋'
          },
          {
            label: 'تغيير صور البوتات',
            description: 'تغيير صورة جميع البوتات في الاشتراك',
            value: 'change_avatars',
            emoji: '🖼️'
          },
          {
            label: 'تغيير أسماء البوتات',
            description: 'تغيير أسماء جميع البوتات في الاشتراك',
            value: 'change_names',
            emoji: '📝'
          },
          {
            label: 'تغيير حالة البوتات',
            description: 'تغيير حالة/نشاط جميع البوتات في الاشتراك',
            value: 'set_game_all',
            emoji: '🎮'
          },
          {
            label: 'تحديد شات الأوامر',
            description: 'تحديد شات محدد لاستقبال أوامر البوتات',
            value: 'set_chat',
            emoji: '💬'
          },
          {
            label: 'إلغاء تحديد شات الأوامر',
            description: 'إلغاء تحديد شات الأوامر والسماح بجميع الشاتات',
            value: 'dis_chat',
            emoji: '🚫'
          }
        ]);

      const settingsEmbed = new Discord.EmbedBuilder()
        .setColor("Blue")
        .setTitle("التحكم في البوتات")
        .setDescription("اختر الإعداد الذي تريد التحكم به من القائمة أدناه")

        .setTimestamp();

      const row = new Discord.ActionRowBuilder().addComponents(settingsMenu);

      const settingsMsg = await message.channel.send({ embeds: [settingsEmbed], components: [row] });
      bot_messages.set(message.id, settingsMsg);
      setTimeout(() => bot_messages.delete(message.id), 300000); // 5 دقائق

      // إنشاء مجمع للتفاعلات
      const collector = settingsMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 300000 // 5 دقائق
      });

      collector.on('collect', async interaction => {
        try {
          if (interaction.isStringSelectMenu() && interaction.customId === 'settings_menu') {
            const selectedOption = interaction.values[0];

            switch (selectedOption) {
              case 'add_owner':
                await handleAddOwner(interaction, music_client, music_db, data);
                break;
              case 'remove_owner':
                await handleRemoveOwner(interaction, music_client, music_db, data);
                break;
              case 'list_owners':
                await handleListOwners(interaction, music_client, data);
                break;
              case 'change_avatars':
                await handleChangeAvatars(interaction, music_client);
                break;
              case 'change_names':
                await handleChangeNames(interaction, music_client);
                break;
              case 'set_game_all':
                await handleSetGameAll(interaction, music_client, music_db);
                break;
              case 'set_chat':
                await handleSetChat(interaction, music_client, music_db);
                break;
              case 'dis_chat':
                await handleDisChat(interaction, music_client, music_db);
                break;
            }
          }
        } catch (error) {
          console.error('Error in settings collector:', error);
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
              content: 'حدث خطأ أثناء معالجة طلبك.',
              ephemeral: true
            }).catch(() => {});
          }
        }
      });

      collector.on('end', () => {
        // تعطيل القائمة عند انتهاء الوقت
        settingsMenu.setDisabled(true);
        const disabledRow = new Discord.ActionRowBuilder().addComponents(settingsMenu);

        const timeoutEmbed = new Discord.EmbedBuilder()
          .setColor("Red")
          .setTitle("⏰ انتهت جلسة الإعدادات")
          .setDescription("انتهت مهلة إدارة الإعدادات. استخدم الأمر مرة أخرى للمتابعة.")
          .setFooter({ text: "يمكنك استخدام أمر settings مرة أخرى" })
          .setTimestamp();

        settingsMsg.edit({ embeds: [timeoutEmbed], components: [disabledRow] }).catch(() => {});
      });
    }
    else if (message.mentions.users.has(music_client.user.id) && args.includes("mybots")) {
      if (!config.owners.find(r => r === message.author.id)) return message.reply({ content: "❌ You do not have permission to use this command." });
      try {
        let sub_data = await sub_db.get(process.env.owner_id);
        if (!sub_data || !Array.isArray(sub_data) || sub_data.length === 0) {
          return message.reply({ content: "❌ No subscription data found." });
        }
        let subscription = sub_data.find(s => s.id == process.env.subscription_id);
        if (!subscription) {
          return message.reply({ content: "❌ No subscription found with your subscription ID." });
        }
        let guild = message.guild;
        let botStatuses = subscription.bots.map(bot => {
          let botId = bot.botId || bot.botToken; // fallback if botId not present
          let botInGuild = guild.members.cache.some(member => member.user.id === botId);
          return `- [Bot Link](https://discord.com/oauth2/authorize?client_id=${botId}&permissions=8&scope=bot%20applications.commands&guild_id=${subscription.guildID}&disable_guild_select=true) - ${botInGuild ? "✅ In Server" : "❌ Not in Server"}`;
        });
        if (botStatuses.length === 0) {
          return message.reply({ content: "❌ No bots found in your subscription." });
        }
        let embed = new Discord.EmbedBuilder()
          .setTitle("Your Bots")
          .setDescription(botStatuses.join("\n"))
          .setColor("Blue");
        message.reply({ embeds: [embed] });
      } catch (error) {
        console.error("Error in mybots command:", error);
        message.reply({ content: "❌ An error occurred while fetching your bots." });
      }
    }
    else if (args[0] === "comeall") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let channel = message.member.voice.channelId ? message.guild.channels.cache.get(message.member.voice.channelId) : null;
      if (!channel) return message.react("❌").catch(() => 0);
      data.channelId = message.member.voice.channelId;

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Set channelId to ${message.member.voice.channelId}`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }
      try {
        await music_client.distube.voices.leave(channel);
      } catch (error) {
        console.error("Error leaving voice channel:", error);
      }
      try {
        await music_client.distube.voices.join(channel);
      } catch (error) {
        console.error("Error joining voice channel:", error);
      }
      message.react("✅").catch(() => 0);
    }
    else if (args[0] === "avatarall") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let avatar = message.attachments.size > 0 ? message.attachments.first().url : args[1];
      if (!avatar) return message.react("❌").catch(() => 0);
      music_client.user.setAvatar(avatar).then(() => {
        message.react("✅").catch(() => 0);
      }).catch(() => {
        message.react("❌").catch(() => 0);
      });
    }
    else if (args[0] === "nameall") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let name = args.slice(1).join(" ");
      if (!name) return message.react("❌").catch(() => 0);
      music_client.user.setUsername(name).then(() => {
        message.react("✅").catch(() => 0);
      }).catch(() => {
        message.react("❌").catch(() => 0);
      });
    }
    else if (args[0] === "setchat") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let channel = message.mentions.channels.first() || message.channel;
      data.commandsChat = channel.id;
      channelId = channel.id

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Set commandsChat to ${channel.id}`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }
      message.react("✅")
    }
    else if (args[0] === "dischat") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      data.commandsChat = null;
      channelId = null

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Disabled commandsChat`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }
      message.react("✅")
    }
  });

  music_client.on("messageCreate", async message => {
    if (!message.guild || message.author.bot) return;
    let args = message.content.split(" ");
    let prefix;
    if (args[0] === `<@${music_client.user.id}>` || args[0] === `<@!${music_client.user.id}>` || args[0] === `${music_client.user.id}`) {
      prefix = "";
      args = args.slice(1);
    } else if (message.member.voice.channelId && message.member.voice.channelId === message.guild.members.me.voice.channelId) {
      prefix = "";
    } else {
      return;
    }
    if (args[0] === prefix + "setname") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let name = args.slice(1).join(" ");
      if (!name) return message.reply({ content: `❌ Please provide a name` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      music_client.user.setUsername(name).then(() => {
        message.reply({ content: `✅ Done change my new name` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      }).catch(() => {
        message.reply({ content: `❌ I can't change my name` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      });
    }
    else if (args[0] === prefix + "setavatar") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let avatar = message.attachments.size > 0 ? message.attachments.first().url : args[1];
      if (!avatar) return message.reply({ content: `❌ Please upload an image or provide his link` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      music_client.user.setAvatar(avatar).then(() => {
        message.reply({ content: `> ✅ Done changed my avatar` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      }).catch(() => {
        message.reply({ content: `❌ I can't change my avatar, please provide valid avatar` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      });
    }
    else if (args[0] === prefix + "setgame") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let name = args.slice(1).join(" ");
      if (!name) return message.reply({ content: `❌ Please provide game text` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      name = name.substring(0, 36);
      let btn_1 = new Discord.ButtonBuilder()
        .setCustomId(`${Discord.ActivityType.Playing}`)
        .setLabel("Playing")
        .setStyle(Discord.ButtonStyle.Secondary);
      let btn_2 = new Discord.ButtonBuilder()
        .setCustomId(`${Discord.ActivityType.Watching}`)
        .setLabel("Watching")
        .setStyle(Discord.ButtonStyle.Secondary);
      let btn_3 = new Discord.ButtonBuilder()
        .setCustomId(`${Discord.ActivityType.Streaming}`)
        .setLabel("Streaming")
        .setStyle(Discord.ButtonStyle.Secondary);
      let btn_4 = new Discord.ButtonBuilder()
        .setCustomId(`${Discord.ActivityType.Listening}`)
        .setLabel("Listening")
        .setStyle(Discord.ButtonStyle.Secondary);
      let btn_5 = new Discord.ButtonBuilder()
        .setCustomId(`${Discord.ActivityType.Competing}`)
        .setLabel("Competing")
        .setStyle(Discord.ButtonStyle.Secondary);
      let row = new Discord.ActionRowBuilder()
        .addComponents(btn_1, btn_2, btn_3, btn_4, btn_5);
      let msg = await message.reply({ content: `Choose game type you want to set`, components: [row] });
      if (!msg) return;
      bot_messages.set(message.id, msg);
      setTimeout(() => bot_messages.delete(message.id), 1800000);
      let collect = await msg.awaitMessageComponent({ filter: m => m.user.id === message.author.id });
      if (!collect || !collect.customId) return;
      try {
        await collect.deferUpdate().catch(error => {
          console.error("Error deferring update:", error);
        });
      } catch (error) {
        console.error("Error in collect handling:", error);
      }
      let activity = {
        name,
        type: Number(collect.customId)
      }
      if (activity.type == Discord.ActivityType.Streaming) {
        activity.url = "https://twitch.tv/faithful_music_bots";
      }
      data.activity = activity;

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Set activity`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }

      // تحديث حالة البوت
      try {
        music_client.user.setPresence({
          activities: [data.activity]
        });
      } catch (error) {
        console.error("Error setting presence:", error);
      }
      msg.edit({ content: `> ✅ Done setup new game`, components: [] }).catch(error => console.error("Error editing message:", error));
    }

    else if (args[0] === prefix + "setup") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!message.member.permissions.has("Administrator") && !data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let channel = message.member.voice.channelId ? message.guild.channels.cache.get(message.member.voice.channelId) : null;
      if (!channel) return message.reply({ content: `❌ You must be in voice channel to use that` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      data.channelId = message.member.voice.channelId;

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Set channelId to ${message.member.voice.channelId} (setup)`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }
      try {
        await music_client.distube.voices.leave(channel);
      } catch (error) {
        console.error("Error leaving voice channel:", error);
      }
      try {
        await music_client.distube.voices.join(channel);
      } catch (error) {
        console.error("Error joining voice channel:", error);
      }
      music_client.user.setUsername(channel.name).catch(error => console.error("Error setting username:", error));
      message.reply({ content: `> ✅ Done setup the bot` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
    }
    else if (args[0] === prefix + "come") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!message.member.permissions.has("Administrator") && !data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;
      let channel = message.member.voice.channelId ? message.guild.channels.cache.get(message.member.voice.channelId) : null;
      if (!channel) return message.reply({ content: `❌ You must be in voice channel to use that` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      data.channelId = message.member.voice.channelId;

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Set channelId to ${message.member.voice.channelId} (come)`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }
      try {
        await music_client.distube.voices.leave(channel);
      } catch (error) {
        console.error("Error leaving voice channel:", error);
      }
      try {
        await music_client.distube.voices.join(channel);
      } catch (error) {
        console.error("Error joining voice channel:", error);
      }
      message.reply({ content: `> ✅ Done connect to your voice channel` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
    }
    else if (args[0] === prefix + "play" || args[0] === prefix + "P" || args[0] === prefix + "p" || args[0] === prefix + "Play" || args[0] === prefix + "ش" || args[0] === prefix + "شغل") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let client_m = message.guild.members.me;
      if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let song = args.slice(1).join(" ");
      if (!song) {
        const embed = new Discord.EmbedBuilder()
          .setColor("#36393f")
          .setTitle("Play Usage")
          .setDescription("**play [اسم الأغنية] - play track by the first result\nplay [رابط الأغنية] - play track by provided link**")
          .setThumbnail(message.guild.iconURL({ dynamic: true }))
        return message.reply({ embeds: [embed] }).then(msg => {
          bot_messages.set(message.id, msg);
          setTimeout(() => bot_messages.delete(message.id), 1800000)
        });
      }
      if (song.split(" ")[0].includes("soundcloud.com") && song.split(" ")[0].includes("http")) {
        song = await getWebSoundCloudURL(song.split(" ")[0]);
      }

      // التحقق من الاتصال بالقناة الصوتية قبل التشغيل
      const voiceChannel = message.member.voice.channel;
      if (!voiceChannel) {
        return safeSendMessage(message.channel, {
          content: "❌ يجب أن تكون في قناة صوتية لتشغيل!"
        });
      }



      try {
        await music_client.distube.play(voiceChannel, song, {
          member: message.member,
          message,
          textChannel: message.channel
        });
      } catch (error) {
        console.error("Error playing song:", error);
        safeSendMessage(message.channel, {
          content: `خطا في تشغيل الأغنية .`
        }).then(msg => {
          if (msg) {
            bot_messages.set(message.id, msg);
            setTimeout(() => bot_messages.delete(message.id), 1800000)
          }
        });
      }
      let queue = music_client.distube.getQueue(message);
      if (queue) {
        let data = await music_db.get(music_client.user.id);
        if (!data) {
          data = defaultData;
        }
        try {
          queue.setVolume(data.volume);
          queue.setRepeatMode(data.repeat ? 1 : 0);
        } catch (error) {
          console.error("Error setting queue properties:", error);
        }
      }
    }
    else if (args[0] === prefix + "stop" || args[0] === prefix + "وقف" || args[0] === prefix + "Stop" || args[0] === prefix + "st") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let client_m = message.guild.members.me;
      if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let queue = music_client.distube.getQueue(message);
      if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      try {
        queue.stop();
      } catch (error) {
        console.error("Error stopping queue:", error);
      }

      const embed = new Discord.EmbedBuilder()
        .setColor("#36393f")
        .setTitle("Queue")
        .setDescription(`Playback has been stopped and 0 tracks in queue has been cleared.`)
        .setThumbnail(message.guild.iconURL({ dynamic: true }))
      message.reply({ embeds: [embed] }).then(msg => {
        bot_messages.set(message.id, msg);
        setTimeout(() => bot_messages.delete(message.id), 1800000)
      });
    }
    else if (args[0] === prefix + "skip" || args[0] === prefix + "س" || args[0] === prefix + "S" || args[0] === prefix + "s" || args[0] === prefix + "Skip" || args[0] === prefix + "تخطي") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let client_m = message.guild.members.me;
      if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let queue = music_client.distube.getQueue(message);
      if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      try {
        if (queue.songs.length > 1) {
          queue.skip();
        } else {
          queue.stop();
        }
      } catch (error) {
        console.error("Error skipping/stopping queue:", error);
      }
      const embed = new Discord.EmbedBuilder()
        .setColor("#36393f")
        .setTitle("Skip")
        .setDescription(`[${queue.songs[0].name}](${queue.songs[0].url})

            **By** - <@${message.author.id}>`)
        .setThumbnail(message.guild.iconURL({ dynamic: true }))
      safeSendMessage(message.channel, { embeds: [embed] }).then(msg => {
        if (msg) {
          bot_messages.set(message.id, msg);
          setTimeout(() => bot_messages.delete(message.id), 1800000)
        }
      });
    }
    else if (args[0] === prefix + "pause" || args[0] === prefix + "Pause" || args[0] === prefix + "توقف") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let client_m = message.guild.members.me;
      if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let queue = music_client.distube.getQueue(message);
      if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      if (!queue.paused) {
        try {
          queue.pause();
        } catch (error) {
          console.error("Error pausing queue:", error);
        }
      }
      message.reply({ content: `> :notes: Paused **${queue.songs[0].name}**. Type \`/resume\` to unpause!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
    }
    else if (args[0] === prefix + "resume" || args[0] === prefix + "Resume" || args[0] === prefix + "كمل") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let client_m = message.guild.members.me;
      if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let queue = music_client.distube.getQueue(message);
      if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      if (queue.paused) {
        try {
          queue.resume();
        } catch (error) {
          console.error("Error resuming queue:", error);
        }
        message.reply({ content: `> :notes: Resumed **${queue.songs[0].name}**.` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      } else {
        message.reply({ content: `> :notes: **${queue.songs[0].name}** doesn't paused already.` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      }
    }


    else if (args[0] === prefix + "nowplaying" || args[0] === prefix + "np" || args[0] === prefix + "الان") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      let queue = music_client.distube.getQueue(message);
      if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let song = queue.songs[0];

      // إنشاء Embed بتصميم مطابق للصورة
      let embed = new Discord.EmbedBuilder()
        .setColor("#36393f")
        .setThumbnail(message.guild.iconURL({ dynamic: true }))
        .setTitle("Now Playing")
        .setDescription(`[${song.name}](${song.url})

**Duration -  ${song.formattedDuration}**

          `)
        // إضافة شريط التقدم كحقل منفصل في نفس الـ Embed
        .addFields(
          {
            name: "\u200B",
            value: `\`▶️ 🔴${"─".repeat(15)} ${formatTime(queue.currentTime * 1000)}`,
            inline: false
          }
        );

      message.reply({ embeds: [embed] }).then(async msg => {
        bot_messages.set(message.id, msg);
        setTimeout(() => bot_messages.delete(message.id), 1800000);

        // تحديث الرسالة كل 5 ثوانٍ
        let interval = setInterval(async () => {
          queue = music_client.distube.getQueue(message);
          if (!queue) {
            clearInterval(interval);
            return msg.delete().catch(error => console.error("Error deleting message:", error));
          }

          song = queue.songs[0];

          // تحديث Embed
          embed = new Discord.EmbedBuilder()
            .setColor("#36393f")
            .setThumbnail(message.guild.iconURL({ dynamic: true }))        // هنا الخطأ
            .setTitle("Now Playing")
            .setDescription(`[${song.name}](${song.url})

**Duration -  ${song.formattedDuration}**

          `)

            // تحديث شريط التقدم
            .addFields(
              {
                name: "\u200B",
                value: createProgressBar(queue.currentTime, song.duration),
                inline: false
              }
            );

          msg.edit({ embeds: [embed] }).catch(err => {
            console.error("Error updating nowplaying message:", err);
            clearInterval(interval);
          });
        }, 5000);
      });
    }






    else if (args[0] === prefix + "volume" || args[0] === prefix + "Volume" || args[0] === prefix + "V" || args[0] === prefix + "v" || args[0] === prefix + "Vol" || args[0] === prefix + "vol" || args[0] === prefix + "صوت") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      let volume = args[1];
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!volume) {
        const embed = new Discord.EmbedBuilder()
          .setColor("#36393f")
          .setTitle("Volume Level")
          .setDescription(`Current volume is: ${data.volume}`)
          .setThumbnail(message.guild.iconURL({ dynamic: true }))
        message.reply({ embeds: [embed] }).then(msg => {
          bot_messages.set(message.id, msg);
          setTimeout(() => bot_messages.delete(message.id), 1800000)
        });
      } else {
        if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
        let client_m = message.guild.members.me;
        if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
        let queue = music_client.distube.getQueue(message);
        if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
        volume = parseInt(volume);
        if (isNaN(volume) || volume < 0 || volume > 100) return message.reply({ content: `🚫 Volume must be a valid integer between 0 and 100!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
        let oldVol = data.volume;
        data.volume = volume;

        // تحديث قاعدة البيانات في الذاكرة
        await music_db.set(music_client.user.id, data);

        // تحديث الملف مباشرة
        try {
          const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
          musicData[music_client.user.id] = data;
          fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
          console.log(`Updated music.json for bot ${music_client.user.id} - Set volume to ${volume}`);
        } catch (error) {
          console.error(`Error updating music.json:`, error);
        }


        // تغيير مستوى الصوت بشكل تدريجي
        await changeVolumeGradually(queue, oldVol, volume);
        async function changeVolumeGradually(queue, oldVol, newVol, delay = 100) {
          if (!queue) return;

          let currentVolume = oldVol;

          const step = newVol > oldVol ? 1 : -1;

          while (currentVolume !== newVol) {
            currentVolume += step;
            queue.setVolume(currentVolume);
            await new Promise(resolve => setTimeout(resolve, delay));
          }

          // تأكيد الوصول إلى القيمة النهائية
          queue.setVolume(newVol);
        }




        const embed = new Discord.EmbedBuilder()
          .setColor("#36393f")
          .setTitle("Volume Level")
          .setDescription(`Current volume is: ${volume}`)
          .setThumbnail(message.guild.iconURL({ dynamic: true }))

        message.reply({ embeds: [embed] }).then(msg => {
          bot_messages.set(message.id, msg);
          setTimeout(() => bot_messages.delete(message.id), 1800000)
        });
      }
    }
    else if (args[0] === prefix + "queue" || args[0] === prefix + "List" || args[0] === prefix + "Queue" || args[0] === prefix + "Qu" || args[0] === prefix + "qu" || args[0] === prefix + "list" || args[0] === prefix + "قائمة") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      let queue = await music_client.distube.getQueue(message);
      let no_embed = new Discord.EmbedBuilder()
        .setTitle("**🎶 Current Queue | 0 entries**")
        .setFooter({ text: "Page 1/1" });
      if (!queue || queue.songs.length < 1) return message.reply({ content: `> **☹️ No music playing.**`, embeds: [no_embed] }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let now_playing = queue.songs[0];
      now_playing = `> ${queue.paused ? "⏸️" : "▶️"} **${now_playing.name}**`;
      if (queue.songs.length === 1) return message.reply({ content: `${now_playing}`, embeds: [no_embed] }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let queue_map = queue.songs.slice(1).map((d, b) => `**[${b += 1}]** ${d.name} - ${d.user}`);
      let embeds = [];
      let k = 10;
      for (let i = 0; i < queue_map.length; i += k) {
        let des = queue_map.slice(i, i + k);
        let embed = new Discord.EmbedBuilder()
          .setTitle(`**🎶 Current Queue | ${queue_map.length} entries**`)
          .setDescription(des.join("\n"))
          .setFooter({ text: `Page ${((i + k) / 10)}/${Math.ceil(queue_map.length / 10)}` });
        embeds.push(embed);
      }
      if (embeds.length <= 1) return message.reply({ content: `${now_playing}`, embeds: [embeds[0]] }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let btn_1 = new Discord.ButtonBuilder()
        .setLabel("Previous")
        .setEmoji("⬅️")
        .setCustomId("left")
        .setStyle(Discord.ButtonStyle.Primary);
      let btn_2 = new Discord.ButtonBuilder()
        .setLabel("Next")
        .setEmoji("➡️")
        .setCustomId("right")
        .setStyle(Discord.ButtonStyle.Primary);
      let row = new Discord.ActionRowBuilder()
        .addComponents(btn_1, btn_2);
      let page = args[1];
      if (!page || isNaN(page)) page = 1;
      page = parseInt(page);
      page -= 1;
      if (page < 0) page = 0;
      if (page + 1 > embeds.length) page = embeds.length - 1;
      message.reply({ content: `${now_playing}`, embeds: [embeds[page]], components: [row] }).then(async msg => {
        bot_messages.set(message.id, msg);
        setTimeout(() => bot_messages.delete(message.id), 1800000);
        let collector = msg.createMessageComponentCollector({ filter: b => b.user.id === message.author.id, time: 3600000 });
        collector.on("collect", async button => {
          try {
            try {
              await button.deferUpdate().catch(error => {
                console.error("Error deferring update:", error);
              });
            } catch (error) {
              console.error("Error in button handling:", error);
            }

            if (button.customId === "right") {
              if (page + 1 >= embeds.length) {
                page = 0;
              } else {
                page += 1;
              }
              msg.edit({ embeds: [embeds[page]] }).catch(error => console.error("Error editing message:", error));
            } else if (button.customId === "left") {
              if (page <= 0) {
                page = embeds.length - 1;
              } else {
                page -= 1;
              }
              msg.edit({ embeds: [embeds[page]] }).catch(error => console.error("Error editing message:", error));
            }
          } catch (error) {
            console.error("Error in queue collector:", error);
          }
        });
        collector.on("end", () => {
          msg.edit({ components: [] }).catch(error => console.error("Error removing components:", error));
        })
      });
    }
    else if (args[0] === prefix + "repeat" || args[0] === prefix + "Repeat" || args[0] === prefix + "Loop" || args[0] === prefix + "كرر" || args[0] === prefix + "loop" || args[0] === prefix + "تكرار") {
      if (channelId && channelId !== message.channel.id) return message.react("❌")
      if (!message.member.voice.channelId) return message.reply({ content: `:no_entry_sign: You must join a voice channel to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let client_m = message.guild.members.me;
      if (client_m.voice.channelId && message.member.voice.channelId !== client_m.voice.channelId) return message.reply({ content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let queue = music_client.distube.getQueue(message);
      if (!queue) return message.reply({ content: `:no_entry_sign: There must be music playing to use that!` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      let repeat = data.repeat ? false : true;
      data.repeat = repeat;

      // تحديث قاعدة البيانات في الذاكرة
      await music_db.set(music_client.user.id, data);

      // تحديث الملف مباشرة
      try {
        const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
        musicData[music_client.user.id] = data;
        fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
        console.log(`Updated music.json for bot ${music_client.user.id} - Set repeat to ${repeat}`);
      } catch (error) {
        console.error(`Error updating music.json:`, error);
      }
      try {
        queue.setRepeatMode(repeat ? 1 : 0);
      } catch (error) {
        console.error("Error setting repeat mode:", error);
      }
      message.reply({ content: `> 🎶 Repeat Mode: **${data.repeat ? "ON" : "OFF"}**` }).then(msg => { bot_messages.set(message.id, msg); setTimeout(() => bot_messages.delete(message.id), 1800000) });
    }

    else if (args[0] === prefix + "help") {
      console.log(message.content)
      if (channelId && channelId !== message.channel.id) return message.react("❌");
      const embed = new EmbedBuilder()
        .setTitle('Music Commands :')
        .setDescription(`
    **<bot> play : تشغيل
      <bot> stop : ايقاف
      <bot> skip : تخطي
      <bot> nowplaying : عرض قائمة التشغيل الحاليه
      <bot> queue : عرض قائمة التشغيل بالكامل
      <bot> pause : ايقاف التشغيل الحالي مؤقت
      <bot> resume : فك ايقاف التشغيل الحالي
      <bot> volume : عرض مستوى الصوت وتغيير مستوى الصوت
      <bot> repeat : تكرار التشغيل الحالي ، وفك التكرار الحالي

      -

      addowner : إضافة اونر لتحكم في البوتات
      removeowner : ازالة اونر من التحكم في البوتات
      owners : قائمة الاونرات
      avatarall : تغير جميع صور البوتات
      nameall : تغير جميع اسامي البوتات
      comeall : سحب جميع البوتات الى رومك
      restartll : اعادة تشغيل جميع البوتات
      setchat : تحديد شات يستقبل اوامر البوتات
      dischat : الغاء تحديد شات يستقبل اوامر البوتات
      <bot> setup : تثبيت البوت في روم معين مع تغير اسم البوت الى اسم الروم
      <bot> setname : تغير اسم البوت
      <bot> setavatar : تغير صورة البوت
      <bot> setgame : تغير حالة البوت
      <bot> come : سحب البوت الى رومك**`)
      try {
        await message.author.send({ embeds: [embed] });
        message.react("✅");
      } catch (error) {
        console.error("Error sending help message:", error);
        message.react("❌");
      }

    }
    else if (args[0] === prefix + "restart") {
      let data = await music_db.get(music_client.user.id);
      if (!data) {
        data = defaultData;
      }
      if (!data.owners.find(o => o === message.author.id) && !config.owners.find(r => r === message.author.id)) return;

      // إرسال رسالة تأكيد قبل إعادة التشغيل
      const confirmEmbed = new Discord.EmbedBuilder()
        .setColor("Orange")
        .setThumbnail(message.guild.iconURL({ dynamic: true }))
        .setTitle("⚠️ تأكيد إعادة التشغيل")
        .setDescription("هل أنت متأكد من أنك تريد إعادة تشغيل جميع البوتات؟")
        .setFooter({ text: "سيتم إلغاء العملية بعد 30 ثانية" });

      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
            .setCustomId("confirm_restart")
            .setLabel("تأكيد")
            .setStyle(Discord.ButtonStyle.Success),
          new Discord.ButtonBuilder()
            .setCustomId("cancel_restart")
            .setLabel("إلغاء")
            .setStyle(Discord.ButtonStyle.Danger)
        );

      const confirmMsg = await message.reply({ embeds: [confirmEmbed], components: [row] });
      bot_messages.set(message.id, confirmMsg);

      // إنشاء مجمع للأزرار
      const collector = confirmMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 30000 // 30 ثانية
      });

      collector.on("collect", async (interaction) => {
        if (interaction.customId === "confirm_restart") {
          // استخدام deferUpdate بدلاً من reply لتجنب خطأ "Interaction has already been acknowledged"
          try {
            await interaction.deferUpdate().catch(err => {
              console.error("Error deferring update:", err);
            });
          } catch (error) {
            console.error("Error in interaction handling:", error);
          }

          // تحديث الرسالة لإظهار أن العملية قيد التنفيذ
          const processingEmbed = new Discord.EmbedBuilder()
            .setColor("Blue")
            .setTitle("🔄 جاري إعادة التشغيل...")
            .setDescription("يرجى الانتظار قليلاً حتى تكتمل العملية.");

          await confirmMsg.edit({ embeds: [processingEmbed], components: [] });

          // محاولة إعادة تشغيل البوت باستخدام PM2
          try {
            pm2.connect(async (err) => {
              if (err) {
                console.error(err);
                const errorEmbed = new Discord.EmbedBuilder()
                  .setColor("Red")
                  .setTitle("❌ فشل الاتصال")
                  .setDescription("حدث خطأ أثناء محاولة الاتصال بمدير العمليات .");

                await confirmMsg.edit({ embeds: [errorEmbed], components: [] });
                return;
              }

              pm2.restart(process.env.subscription_id, async (err) => {
                if (err) {
                  console.error(err);
                  const errorEmbed = new Discord.EmbedBuilder()
                    .setColor("Red")
                    .setTitle("❌ فشل إعادة التشغيل")
                    .setDescription("حدث خطأ أثناء محاولة إعادة تشغيل البوتات.");

                  await confirmMsg.edit({ embeds: [errorEmbed], components: [] });
                } else {
                  const successEmbed = new Discord.EmbedBuilder()
                    .setColor("Green")
                    .setTitle("✅ تمت إعادة التشغيل بنجاح")
                    .setDescription("تمت إعادة تشغيل البوتات بنجاح. قد يستغرق الأمر بضع ثوانٍ حتى تعود البوتات للعمل بشكل كامل.");

                  await confirmMsg.edit({ embeds: [successEmbed], components: [] });
                }

                // قطع الاتصال بـ PM2 بعد الانتهاء
                pm2.disconnect();
              });
            });
          } catch (error) {
            console.error("Error during restart:", error);
            const errorEmbed = new Discord.EmbedBuilder()
              .setColor("Red")
              .setTitle("❌ حدث خطأ غير متوقع")
              .setDescription("حدث خطأ غير متوقع أثناء محاولة إعادة تشغيل البوتات.");

            await confirmMsg.edit({ embeds: [errorEmbed], components: [] });
          }
        } else if (interaction.customId === "cancel_restart") {
          // استخدام deferUpdate بدلاً من reply لتجنب خطأ "Interaction has already been acknowledged"
          try {
            await interaction.deferUpdate().catch(err => {
              console.error("Error deferring update:", err);
            });
          } catch (error) {
            console.error("Error in interaction handling:", error);
          }

          const cancelEmbed = new Discord.EmbedBuilder()
            .setColor("Grey")
            .setTitle("🚫 تم إلغاء العملية")
            .setDescription("تم إلغاء عملية إعادة التشغيل.");

          await confirmMsg.edit({ embeds: [cancelEmbed], components: [] });
          collector.stop();
        }
      });

      collector.on("end", async (collected, reason) => {
        if (reason === "time" && collected.size === 0) {
          const timeoutEmbed = new Discord.EmbedBuilder()
            .setColor("Grey")
            .setTitle("⏱️ انتهت المهلة")
            .setDescription("انتهت مهلة التأكيد. تم إلغاء عملية إعادة التشغيل.");

          await confirmMsg.edit({ embeds: [timeoutEmbed], components: [] });
        }
      });
    }
  });

  music_client.on("voiceStateUpdate", async (oldState, newState) => {
    if (newState.id != music_client.user.id) return;
    let data = await music_db.get(music_client.user.id);
    if (!data || !data.channelId) return;
    if (newState.channelId === data.channelId || oldState.channelId === newState.channelId) return;
    let channel = music_client.channels.cache.get(data.channelId);
    if (!channel) return;
    try {
      if (newState.channelId) {
        music_client.distube.voices.join(channel).catch(error => console.error("Error joining voice channel:", error));
      } else {
        // إعادة الانضمام بعد 3 ثوانٍ إذا تم فصل البوت
        setTimeout(() => {
          // التحقق مرة أخرى من حالة القناة
          if (newState.channelId === data.channelId || oldState.channelId === newState.channelId) return;

          try {
            joinVoiceChannel({
              channelId: channel.id,
              guildId: channel.guild.id,
              adapterCreator: channel.guild.voiceAdapterCreator,
            });
          } catch (error) {
            console.error("Error rejoining voice channel:", error);
          }
        }, 3000);
      }

    } catch (error) {
      console.error("Error in voiceStateUpdate event:", error);
    }
  });

  // دالة مساعدة للتحقق من صلاحيات إرسال الرسائل
  async function canSendMessage(channel) {
    try {
      if (!channel || !channel.guild) return false;

      const botMember = await channel.guild.members.fetch(music_client.user.id);
      const permissions = channel.permissionsFor(botMember);

      return permissions &&
             permissions.has(Discord.PermissionFlagsBits.SendMessages) &&
             permissions.has(Discord.PermissionFlagsBits.EmbedLinks);
    } catch (error) {
      console.error("Error checking channel permissions:", error);
      return false;
    }
  }

  // دالة مساعدة لإرسال الرسائل بأمان
  async function safeSendMessage(channel, messageOptions) {
    try {
      if (!await canSendMessage(channel)) {
        console.log(`Cannot send message to channel ${channel.id}: Missing permissions`);
        return null;
      }

      return await channel.send(messageOptions);
    } catch (error) {
      console.error("Error sending message:", error);
      return null;
    }
  }

  music_client.distube.on("addSong", async (queue, song) => {
    // إذا كانت القائمة فارغة (أول أغنية)، نعرض رسالة "Now Playing"
    if (queue.songs.length <= 1) {

      const embed = new Discord.EmbedBuilder()
        .setColor("#36393f")
        .setThumbnail(queue.textChannel.guild.iconURL({ dynamic: true }))
        .setTitle("Now Listening")
        .setDescription(`\n[${song.name}](${song.url})\n
**Duration - ${song.formattedDuration}**`)

      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
            .setCustomId("repeat")
            .setEmoji("1376828504812949554")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("volume_down")
            .setEmoji("1376829683387338782")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("pause_resume")
            .setEmoji("1376827920294613062")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("volume_up")
            .setEmoji("1376829324056854528")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("skip")
            .setEmoji("1376828270968049775")
            .setStyle(Discord.ButtonStyle.Secondary)
        );

      await safeSendMessage(queue.textChannel, { embeds: [embed], components: [row] });
    } else {
      // إذا كانت هناك أغاني أخرى في القائمة، نعرض رسالة "Added to queue"
      const position = queue.songs.length - 1;
      const estimatedTime = prettyMilliseconds(queue.duration * 1000 - song.duration * 1000);

      const embed = new Discord.EmbedBuilder()
        .setColor("#36393f")
        .setThumbnail(queue.textChannel.guild.iconURL({ dynamic: true }))
        .setTitle(`Added To List`)
        .setDescription(`[${song.name}](${song.url})

**Duration - ${song.formattedDuration}**
          Added to the queue at position - **#${position}**\nEstimated time until playing - **${estimatedTime}**`)

      await safeSendMessage(queue.textChannel, { embeds: [embed] });
    }
  })
    .on("addList", async (queue, playlist) => {
      const embed = new Discord.EmbedBuilder()
        .setColor("#36393f")
        .setThumbnail(queue.textChannel.guild.iconURL({ dynamic: true }))
        .setTitle(`${playlist.name}`)
        .setDescription(`Added to the queue at position #1\nEstimated time until playing: ${prettyMilliseconds(queue.duration * 1000)}`)
      await safeSendMessage(queue.textChannel, { embeds: [embed] });
    })
    .on("searchNoResult", (message, _query) => {
      message.reply({ content: `> **🔍 Not found.**` }).then(msg => {
        bot_messages.set(message.id, msg);
        setTimeout(() => bot_messages.delete(message.id), 1800000);
      }).catch(error => console.error("Error replying to search no result:", error));
    })
    .on("error", async (channel, error) => {
      console.error("DisTube error:", error);

      // إرسال رسالة خطأ للمستخدم
      if (channel && channel.send) {
        await safeSendMessage(channel, {
          content: `❌ حدث خطأ غير معروف`
        });
      }
    })
    .on("disconnect", (queue) => {
      console.log("DisTube disconnected from voice channel");

      // محاولة إعادة الاتصال
      setTimeout(() => {
        if (queue && queue.voiceChannel) {
          try {
            music_client.distube.voices.join(queue.voiceChannel);
            console.log("Reconnected to voice channel");
          } catch (error) {
            console.error("Error reconnecting to voice channel:", error);
          }
        }
      }, 3000);
    })
    .on("initQueue", (queue) => {
      // إعدادات القائمة الافتراضية
      queue.autoplay = false;
      queue.volume = 50;
      console.log("Queue initialized with default settings");
    });

  music_client.login(token).catch(() => console.log("Invalid token:", token));
}

// هذه الدالة غير مستخدمة وتم استبدالها بدالة createProgressBar

// دالة جديدة لإنشاء شريط تقدم بتصميم مختلف للأمر np
function createProgressBar(currentTime, duration) {
  // طول شريط التقدم
  const barLength = 15;

  // حساب نسبة التقدم
  const progress = currentTime / duration;

  // عدد العلامات المملوءة
  const filledLength = Math.round(barLength * progress);

  // إنشاء الشريط
  const emptyChar = "─";
  const filledChar = "━";
  const indicatorChar = "🔘";

  // إنشاء الشريط مع مؤشر التقدم
  let bar = "";

  for (let i = 0; i < barLength; i++) {
    if (i < filledLength) {
      bar += filledChar;
    } else if (i === filledLength) {
      bar += indicatorChar;
    } else {
      bar += emptyChar;
    }
  }

  // إضافة الوقت الحالي والمدة الإجمالية
  return `▶️ ${bar} ${formatTime(currentTime * 1000)} / ${formatTime(duration * 1000)}`;
}

function formatTime(time) {
  try {
    let date = new Date(time).toString().split(" ")[4];
    if (!date) {
      console.error("Invalid time format:", time);
      return "00:00";
    }
    date = date.split(":").length >= 3 ? parseInt(date.split(":")[0]) > 0 ? date : date.split(":").slice(1).join(":") : date;
    return date;
  } catch (error) {
    console.error("Error formatting time:", error);
    return "00:00";
  }
}

// دالة لتحويل الوقت بالميلي ثانية إلى صيغة مقروءة
function prettyMilliseconds(ms) {
  if (ms === 0) return '0:00';

  const seconds = Math.floor((ms / 1000) % 60);
  const minutes = Math.floor((ms / (1000 * 60)) % 60);
  const hours = Math.floor(ms / (1000 * 60 * 60));

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

async function getWebSoundCloudURL(url) {
  try {
    let data = await axios.get(url).catch(error => {
      console.error("Error fetching SoundCloud URL:", error);
      return 0;
    });
    let new_url = data?.request?.res?.responseUrl;
    new_url = new_url ? new_url : url;
    return new_url;
  } catch (error) {
    console.error("Error in getWebSoundCloudURL:", error);
    return url;
  }
}

// دالة لتغيير مستوى الصوت بشكل تدريجي
async function changeVolumeGradually(queue, startVolume, targetVolume, steps = 5, delay = 100) {
  if (!queue) return;

  // حساب حجم الخطوة
  const volumeStep = (targetVolume - startVolume) / steps;

  // تغيير مستوى الصوت تدريجياً
  for (let i = 1; i <= steps; i++) {
    const currentVolume = Math.round(startVolume + (volumeStep * i));
    try {
      queue.setVolume(currentVolume);
    } catch (error) {
      console.error("Error changing volume:", error);
    }

    // انتظار قبل الخطوة التالية
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // التأكد من أن مستوى الصوت النهائي هو المطلوب بالضبط
  try {
    queue.setVolume(targetVolume);
  } catch (error) {
    console.error("Error setting final volume:", error);
  }
}

// دوال مساعدة لإعدادات البوت
async function handleAddOwner(interaction, music_client, music_db, data) {
  // إنشاء Modal لإدخال معرف المستخدم
  const modal = new Discord.ModalBuilder()
    .setCustomId('add_owner_modal')
    .setTitle('إضافة مالك لجميع البوتات');

  const userIdInput = new Discord.TextInputBuilder()
    .setCustomId('user_id')
    .setLabel('معرف المستخدم أو منشن')
    .setPlaceholder('123456789012345678 أو @username')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true);

  const userRow = new Discord.ActionRowBuilder().addComponents(userIdInput);
  modal.addComponents(userRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    let userId = modalSubmit.fields.getTextInputValue('user_id').trim();

    // إزالة المنشن إذا كان موجوداً
    userId = userId.replace(/[<@!>]/g, '');

    // التحقق من صحة معرف المستخدم
    if (!userId || isNaN(userId) || userId.length < 15) {
      return await modalSubmit.reply({
        content: "❌ معرف المستخدم غير صالح. يجب أن يكون مكون من 15-20 رقم.",
        ephemeral: true
      });
    }

    // محاولة جلب المستخدم
    let user;
    try {
      user = await music_client.users.fetch(userId);
    } catch (error) {
      return await modalSubmit.reply({
        content: "❌ لم يتم العثور على مستخدم بهذا المعرف.",
        ephemeral: true
      });
    }

    // التحقق من أن المستخدم ليس مالكاً بالفعل
    if (data.owners.find(o => o === user.id)) {
      return await modalSubmit.reply({
        content: `❌ المستخدم ${user.username} مالك بالفعل.`,
        flags: MessageFlags.Ephemeral
      });
    }

    await modalSubmit.deferReply({ flags: MessageFlags.Ephemeral });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // إضافة المالك لجميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // الحصول على بيانات البوت
          let botData = await music_db.get(bot.botId);
          if (!botData) {
            botData = {
              owners: [],
              prefix: "!",
              color: "Blue"
            };
          }

          // التحقق من أن المستخدم ليس مالكاً بالفعل لهذا البوت
          if (!botData.owners.find(o => o === user.id)) {
            botData.owners.push(user.id);

            // تحديث قاعدة البيانات
            await music_db.set(bot.botId, botData);

            // تحديث الملف
            try {
              const fs = require('fs');
              const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
              musicData[bot.botId] = botData;
              fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
            } catch (error) {
              console.error(`Error updating music.json for bot ${bot.botId}:`, error);
            }

            successCount++;
            results.push(`✅ ${bot.botId}: تم إضافة المالك`);
          } else {
            results.push(`⚠️ ${bot.botId}: مالك بالفعل`);
          }

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الإضافة`);
          console.error(`Error adding owner to bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم إضافة المالك للبوتات`)
        .setDescription(`تم إضافة ${user.username} كمالك لـ ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "المستخدم المضاف",
            value: `<@${user.id}> (${user.username})`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          },
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error adding owner to all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في إضافة المالك")
        .setDescription("حدث خطأ أثناء إضافة المالك لجميع البوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• مشكلة في الوصول لبيانات الاشتراك\n• مشكلة في قاعدة البيانات\n• مشكلة في حفظ الملفات",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in add owner modal:', error);
    if (error.code === 'InteractionCollectorError') {
      await interaction.followUp({
        content: "⏰ انتهت مهلة إدخال البيانات.",
        ephemeral: true
      }).catch(() => {});
    }
  }
}

async function handleRemoveOwner(interaction, music_client, music_db, data) {
  if (data.owners.length === 0) {
    return await interaction.reply({
      content: "❌ لا يوجد مالكين لإزالتهم.",
      ephemeral: true
    });
  }

  // إنشاء قائمة بالمالكين الحاليين
  const ownerOptions = [];
  for (let ownerId of data.owners) {
    try {
      const user = await music_client.users.fetch(ownerId);
      ownerOptions.push({
        label: user.username,
        description: `معرف: ${ownerId}`,
        value: ownerId
      });
    } catch (error) {
      // إذا لم يتم العثور على المستخدم، أضفه بمعرفه فقط
      ownerOptions.push({
        label: `مستخدم غير معروف`,
        description: `معرف: ${ownerId}`,
        value: ownerId
      });
    }
  }

  const removeMenu = new Discord.StringSelectMenuBuilder()
    .setCustomId('remove_owner_menu')
    .setPlaceholder('اختر مالكاً لإزالته من جميع البوتات')
    .addOptions(ownerOptions);

  const removeEmbed = new Discord.EmbedBuilder()
    .setColor("Orange")
    .setTitle("➖ إزالة مالك من جميع البوتات")
    .setDescription("اختر المالك الذي تريد إزالته من جميع البوتات في الاشتراك")
    .addFields([
      {
        name: "تحذير",
        value: "بعد إزالة المالك، لن يتمكن من استخدام أوامر إدارة أي من البوتات في الاشتراك.",
        inline: false
      }
    ])
    .setFooter({ text: "اختر من القائمة أدناه" });

  const removeRow = new Discord.ActionRowBuilder().addComponents(removeMenu);

  await interaction.reply({ embeds: [removeEmbed], components: [removeRow], flags: MessageFlags.Ephemeral });

  // انتظار اختيار المستخدم
  const result = await safeAwaitMessageComponent(interaction.channel, {
    filter: i => i.user.id === interaction.user.id && i.customId === 'remove_owner_menu',
    time: 60000
  });

  if (!result.success) {
    if (result.error === 'timeout') {
      return await safeErrorReply(interaction, "⏰ انتهت مهلة اختيار المالك.", true);
    }
    return await safeErrorReply(interaction, `❌ حدث خطأ: ${result.message}`, true);
  }

  const selectInteraction = result.interaction;

    const ownerIdToRemove = selectInteraction.values[0];

    await safeDeferReply(selectInteraction, true);

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // إزالة المالك من جميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // الحصول على بيانات البوت
          let botData = await music_db.get(bot.botId);
          if (!botData) {
            results.push(`⚠️ ${bot.botId}: لا توجد بيانات`);
            continue;
          }

          // البحث عن المالك وإزالته
          const ownerIndex = botData.owners.findIndex(o => o === ownerIdToRemove);
          if (ownerIndex !== -1) {
            botData.owners.splice(ownerIndex, 1);

            // تحديث قاعدة البيانات
            await music_db.set(bot.botId, botData);

            // تحديث الملف
            try {
              const fs = require('fs');
              const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
              musicData[bot.botId] = botData;
              fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
            } catch (error) {
              console.error(`Error updating music.json for bot ${bot.botId}:`, error);
            }

            successCount++;
            results.push(`✅ ${bot.botId}: تم إزالة المالك`);
          } else {
            results.push(`⚠️ ${bot.botId}: ليس مالكاً`);
          }

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الإزالة`);
          console.error(`Error removing owner from bot ${bot.botId}:`, error);
        }
      }

      let removedUser;
      try {
        removedUser = await music_client.users.fetch(ownerIdToRemove);
      } catch (error) {
        removedUser = { username: "مستخدم غير معروف", id: ownerIdToRemove };
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم إزالة المالك من البوتات`)
        .setDescription(`تم إزالة ${removedUser.username} من ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "المستخدم المُزال",
            value: `<@${ownerIdToRemove}> (${removedUser.username})`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          },
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          }
        ])
        .setTimestamp();

      await selectInteraction.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error removing owner from all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في إزالة المالك")
        .setDescription("حدث خطأ أثناء إزالة المالك من جميع البوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• مشكلة في الوصول لبيانات الاشتراك\n• مشكلة في قاعدة البيانات\n• مشكلة في حفظ الملفات",
            inline: false
          }
        ])
        .setTimestamp();

      await selectInteraction.editReply({ embeds: [errorEmbed] });
    }


}

async function handleListOwners(interaction, music_client, data) {
  if (data.owners.length === 0) {
    const noOwnersEmbed = new Discord.EmbedBuilder()
      .setColor("Orange")
      .setTitle("📋 قائمة المالكين")
      .setDescription("لا يوجد مالكين مضافين حالياً.")
      .setFooter({ text: "استخدم خيار 'إضافة مالك' لإضافة مالكين جدد" })
      .setTimestamp();

    return await interaction.reply({ embeds: [noOwnersEmbed], ephemeral: true });
  }

  // إنشاء قائمة المالكين
  const ownersList = [];
  for (let i = 0; i < data.owners.length; i++) {
    const ownerId = data.owners[i];
    try {
      const user = await music_client.users.fetch(ownerId);
      ownersList.push(`${i + 1}. <@${ownerId}> (${user.username})`);
    } catch (error) {
      ownersList.push(`${i + 1}. <@${ownerId}> (مستخدم غير معروف)`);
    }
  }

  const ownersEmbed = new Discord.EmbedBuilder()
    .setColor("Blue")
    .setTitle("📋 قائمة المالكين")
    .setDescription(ownersList.join("\n"))
    .addFields([
      {
        name: "إجمالي المالكين",
        value: `${data.owners.length} مالك`,
        inline: true
      },
      {
        name: "البوت",
        value: `${music_client.user.username}`,
        inline: true
      }
    ])
    .setFooter({ text: "هؤلاء هم جميع المالكين الحاليين للبوت" })
    .setTimestamp();

  await interaction.reply({ embeds: [ownersEmbed], flags: MessageFlags.Ephemeral });
}

async function handleChangeAvatars(interaction, music_client) {
  // إنشاء Modal لإدخال رابط الصورة
  const modal = new Discord.ModalBuilder()
    .setCustomId('change_avatars_modal')
    .setTitle('تغيير صور جميع البوتات');

  const avatarUrlInput = new Discord.TextInputBuilder()
    .setCustomId('avatar_url')
    .setLabel('رابط الصورة الجديدة لجميع البوتات')
    .setPlaceholder('https://example.com/image.png')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true);

  const avatarRow = new Discord.ActionRowBuilder().addComponents(avatarUrlInput);
  modal.addComponents(avatarRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    const avatarUrl = modalSubmit.fields.getTextInputValue('avatar_url').trim();

    // التحقق من صحة الرابط
    if (!avatarUrl || !avatarUrl.startsWith('http')) {
      return await modalSubmit.reply({
        content: "❌ رابط الصورة غير صالح. يجب أن يبدأ بـ http أو https.",
        flags: MessageFlags.Ephemeral
      });
    }

    await modalSubmit.deferReply({ flags: MessageFlags.Ephemeral });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // تغيير صورة جميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // إنشاء عميل مؤقت للبوت
          const { Client, GatewayIntentBits } = require('discord.js');
          const tempClient = new Client({
            intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildVoiceStates, GatewayIntentBits.GuildMessages]
          });

          await tempClient.login(bot.botToken);
          await tempClient.user.setAvatar(avatarUrl);
          await tempClient.destroy();

          successCount++;
          results.push(`✅ ${tempClient.user?.username || bot.botId}: نجح`);
        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل`);
          console.error(`Error changing avatar for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم تغيير صور البوتات`)
        .setDescription(`تم تحديث صور ${successCount} من ${subscription.bots.length} بوت`)
        .setThumbnail(avatarUrl)
        .addFields([
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error changing avatars:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تغيير الصور")
        .setDescription("حدث خطأ أثناء تغيير صور البوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• الرابط غير صالح\n• الصورة كبيرة جداً\n• مشكلة في الاتصال\n• مشكلة في الوصول لبيانات الاشتراك",
            inline: false
          }
        ])
        .setFooter({ text: "تأكد من أن الرابط يؤدي إلى صورة صالحة" })
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in change avatars modal:', error);
    if (error.code === 'InteractionCollectorError') {
      await interaction.followUp({
        content: "⏰ انتهت مهلة إدخال البيانات.",
        flags: MessageFlags.Ephemeral
      }).catch(() => {});
    }
  }
}

async function handleChangeNames(interaction, music_client) {
  // إنشاء Modal لإدخال الاسم الجديد
  const modal = new Discord.ModalBuilder()
    .setCustomId('change_names_modal')
    .setTitle('تغيير أسماء جميع البوتات');

  const nameInput = new Discord.TextInputBuilder()
    .setCustomId('bot_name')
    .setLabel('الاسم الجديد لجميع البوتات')
    .setPlaceholder('اسم البوتات الجديد')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true)
    .setMaxLength(32);

  const nameRow = new Discord.ActionRowBuilder().addComponents(nameInput);
  modal.addComponents(nameRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    const newName = modalSubmit.fields.getTextInputValue('bot_name').trim();

    // التحقق من صحة الاسم
    if (!newName || newName.length < 2) {
      return await modalSubmit.reply({
        content: "❌ الاسم غير صالح. يجب أن يكون على الأقل حرفين.",
        flags: MessageFlags.Ephemeral
      });
    }

    await modalSubmit.deferReply({ flags: MessageFlags.Ephemeral });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // تغيير أسماء جميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let i = 0; i < subscription.bots.length; i++) {
        let bot = subscription.bots[i];
        try {
          // إنشاء عميل مؤقت للبوت
          const { Client, GatewayIntentBits } = require('discord.js');
          const tempClient = new Client({
            intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildVoiceStates, GatewayIntentBits.GuildMessages]
          });

          await tempClient.login(bot.botToken);
          const oldName = tempClient.user.username;

          // إضافة رقم للاسم إذا كان هناك أكثر من بوت
          const finalName = subscription.bots.length > 1 ? `${newName} ${i + 1}` : newName;

          await tempClient.user.setUsername(finalName);
          await tempClient.destroy();

          successCount++;
          results.push(`✅ ${oldName} → ${finalName}: نجح`);
        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل`);
          console.error(`Error changing name for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم تغيير أسماء البوتات`)
        .setDescription(`تم تحديث أسماء ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          },
          {
            name: "ملاحظة",
            value: "تم إضافة أرقام للأسماء تلقائياً لتمييز البوتات",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error changing names:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تغيير الأسماء")
        .setDescription("حدث خطأ أثناء تغيير أسماء البوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• الاسم مستخدم بالفعل\n• الاسم يحتوي على رموز غير مسموحة\n• تم الوصول لحد التغيير اليومي\n• مشكلة في الوصول لبيانات الاشتراك",
            inline: false
          }
        ])
        .setFooter({ text: "يمكن تغيير اسم البوت مرتين فقط في الساعة" })
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in change names modal:', error);
    if (error.code === 'InteractionCollectorError') {
      await interaction.followUp({
        content: "⏰ انتهت مهلة إدخال البيانات.",
        flags: MessageFlags.Ephemeral
      }).catch(() => {});
    }
  }
}



async function handleSetGameAll(interaction, music_client, music_db) {
  // إنشاء Modal لإدخال حالة النشاط
  const modal = new Discord.ModalBuilder()
    .setCustomId('set_game_all_modal')
    .setTitle('تغيير حالة جميع البوتات');

  const gameInput = new Discord.TextInputBuilder()
    .setCustomId('game_text')
    .setLabel('النشاط/الحالة الجديدة')
    .setPlaceholder('مثال: يستمع إلى الموسيقى')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true)
    .setMaxLength(128);

  const gameRow = new Discord.ActionRowBuilder().addComponents(gameInput);
  modal.addComponents(gameRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    const gameText = modalSubmit.fields.getTextInputValue('game_text').trim();

    // التحقق من صحة النص
    if (!gameText || gameText.length < 1) {
      return await modalSubmit.reply({
        content: "❌ نص الحالة غير صالح. يجب أن يحتوي على حرف واحد على الأقل.",
        flags: MessageFlags.Ephemeral
      });
    }

    await modalSubmit.deferReply({ flags: MessageFlags.Ephemeral });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // حفظ حالة النشاط في قاعدة البيانات لجميع البوتات
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // الحصول على بيانات البوت
          let botData = await music_db.get(bot.botId);
          if (!botData) {
            botData = {
              owners: [],
              prefix: "!",
              color: "Blue"
            };
          }

          // حفظ حالة النشاط في قاعدة البيانات
          botData.game = gameText;

          // تحديث قاعدة البيانات
          await music_db.set(bot.botId, botData);

          // تحديث الملف
          try {
            const fs = require('fs');
            const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
            musicData[bot.botId] = botData;
            fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
          } catch (error) {
            console.error(`Error updating music.json for bot ${bot.botId}:`, error);
          }

          successCount++;
          results.push(`✅ ${bot.botId}: تم حفظ الحالة`);
        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الحفظ`);
          console.error(`Error saving game for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم حفظ حالة البوتات`)
        .setDescription(`تم حفظ حالة ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "الحالة الجديدة",
            value: `🎮 ${gameText}`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          },
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          },
          {
            name: "ملاحظة مهمة",
            value: "تم حفظ الحالة في قاعدة البيانات. ستظهر الحالة الجديدة عند إعادة تشغيل البوتات أو عند تشغيل أي أمر موسيقى.",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error setting game for all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تغيير الحالة")
        .setDescription("حدث خطأ أثناء تغيير حالة البوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• مشكلة في الاتصال بالبوتات\n• مشكلة في الوصول لبيانات الاشتراك\n• مشكلة في تسجيل الدخول",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in set game all modal:', error);
    if (error.code === 'InteractionCollectorError') {
      await interaction.followUp({
        content: "⏰ انتهت مهلة إدخال البيانات.",
        flags: MessageFlags.Ephemeral
      }).catch(() => {});
    }
  }
}

async function handleSetChat(interaction, music_client, music_db) {
  try {
    // الحصول على الشات الحالي
    const currentChannel = interaction.channel;

    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // تحديد شات الأوامر لجميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // الحصول على بيانات البوت
          let botData = await music_db.get(bot.botId);
          if (!botData) {
            botData = {
              owners: [],
              prefix: "!",
              color: "Blue"
            };
          }

          // تحديد شات الأوامر
          botData.commandsChat = currentChannel.id;

          // تحديث قاعدة البيانات
          await music_db.set(bot.botId, botData);

          // تحديث المتغير العام للبوت الحالي
          if (bot.botId === music_client.user.id) {
            channelId = currentChannel.id;
            console.log(`Updated channelId for current bot ${music_client.user.id} to ${currentChannel.id}`);
          }

          // تحديث الملف
          try {
            const fs = require('fs');
            const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
            musicData[bot.botId] = botData;
            fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
          } catch (error) {
            console.error(`Error updating music.json for bot ${bot.botId}:`, error);
          }

          successCount++;
          results.push(`✅ ${bot.botId}: تم تحديد الشات`);

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في التحديد`);
          console.error(`Error setting chat for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم تحديد شات الأوامر`)
        .setDescription(`تم تحديد شات الأوامر لـ ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "الشات المحدد",
            value: `<#${currentChannel.id}> (${currentChannel.name})`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          },
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          },
          {
            name: "ملاحظة",
            value: "البوتات ستستقبل الأوامر من هذا الشات فقط الآن.",
            inline: false
          }
        ])
        .setTimestamp();

      await interaction.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error setting chat for all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تحديد شات الأوامر")
        .setDescription("حدث خطأ أثناء تحديد شات الأوامر للبوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• مشكلة في الوصول لبيانات الاشتراك\n• مشكلة في قاعدة البيانات\n• مشكلة في حفظ الملفات",
            inline: false
          }
        ])
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in set chat:', error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "❌ حدث خطأ أثناء معالجة طلبك.",
        ephemeral: true
      }).catch(() => {});
    }
  }
}

async function handleDisChat(interaction, music_client, music_db) {
  try {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database({
        path: "../../databases/subscriptions.json",
        autoSave: true,
        saveTimeout: 0,
        encryption: false
      });

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // إلغاء تحديد شات الأوامر لجميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // الحصول على بيانات البوت
          let botData = await music_db.get(bot.botId);
          if (!botData) {
            results.push(`⚠️ ${bot.botId}: لا توجد بيانات`);
            continue;
          }

          // إلغاء تحديد شات الأوامر
          botData.commandsChat = null;

          // تحديث قاعدة البيانات
          await music_db.set(bot.botId, botData);

          // تحديث المتغير العام للبوت الحالي
          if (bot.botId === music_client.user.id) {
            channelId = null;
            console.log(`Updated channelId for current bot ${music_client.user.id} to null (disabled)`);
          }

          // تحديث الملف
          try {
            const fs = require('fs');
            const musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
            musicData[bot.botId] = botData;
            fs.writeFileSync('projects/music/databases/music.json', JSON.stringify(musicData, null, 2));
          } catch (error) {
            console.error(`Error updating music.json for bot ${bot.botId}:`, error);
          }

          successCount++;
          results.push(`✅ ${bot.botId}: تم إلغاء التحديد`);

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الإلغاء`);
          console.error(`Error removing chat for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم إلغاء تحديد شات الأوامر`)
        .setDescription(`تم إلغاء تحديد شات الأوامر لـ ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          },
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          },
          {
            name: "ملاحظة",
            value: "البوتات ستستقبل الأوامر من جميع الشاتات الآن.",
            inline: false
          }
        ])
        .setTimestamp();

      await interaction.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error removing chat for all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في إلغاء تحديد شات الأوامر")
        .setDescription("حدث خطأ أثناء إلغاء تحديد شات الأوامر للبوتات")
        .addFields([
          {
            name: "السبب المحتمل",
            value: "• مشكلة في الوصول لبيانات الاشتراك\n• مشكلة في قاعدة البيانات\n• مشكلة في حفظ الملفات",
            inline: false
          }
        ])
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in dis chat:', error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "❌ حدث خطأ أثناء معالجة طلبك.",
        ephemeral: true
      }).catch(() => {});
    }
  }
}

