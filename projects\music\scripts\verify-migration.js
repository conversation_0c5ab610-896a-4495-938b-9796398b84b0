// تحميل متغيرات البيئة
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const { dbConnection } = require('../database/connection');
const musicBotService = require('../services/musicBotService');

async function verifyMigration() {
  console.log('🔍 التحقق من نجاح الهجرة...\n');

  try {
    // الاتصال بقاعدة البيانات
    await dbConnection.connect();

    // قراءة البيانات الأصلية
    const jsonFilePath = path.join(__dirname, '../databases/music.json');
    let originalData = {};
    
    if (fs.existsSync(jsonFilePath)) {
      const data = fs.readFileSync(jsonFilePath, 'utf8');
      originalData = JSON.parse(data);
      console.log(`📊 البيانات الأصلية: ${Object.keys(originalData).length} سجل`);
    } else {
      console.log('⚠️ ملف البيانات الأصلي غير موجود');
      return;
    }

    // الحصول على إحصائيات MongoDB
    const stats = await musicBotService.getStats();
    console.log(`📊 البيانات في MongoDB: ${stats.totalBots} سجل`);

    // مقارنة العدد
    const originalCount = Object.keys(originalData).length;
    if (originalCount === stats.totalBots) {
      console.log('✅ عدد السجلات متطابق');
    } else {
      console.log(`⚠️ عدد السجلات غير متطابق: ${originalCount} أصلي vs ${stats.totalBots} في MongoDB`);
    }

    // التحقق من البيانات التفصيلية
    console.log('\n🔍 التحقق من البيانات التفصيلية...');
    let matchCount = 0;
    let mismatchCount = 0;
    const mismatches = [];

    for (const [botId, originalBotData] of Object.entries(originalData)) {
      try {
        const mongoData = await musicBotService.get(botId);
        
        // مقارنة الحقول الأساسية
        const fieldsToCheck = ['volume', 'repeat', 'channelId', 'owners', 'commandsChat'];
        let isMatch = true;
        const differences = [];

        for (const field of fieldsToCheck) {
          const originalValue = originalBotData[field];
          const mongoValue = mongoData[field];

          if (JSON.stringify(originalValue) !== JSON.stringify(mongoValue)) {
            isMatch = false;
            differences.push({
              field,
              original: originalValue,
              mongo: mongoValue
            });
          }
        }

        if (isMatch) {
          matchCount++;
        } else {
          mismatchCount++;
          mismatches.push({
            botId,
            differences
          });
        }

      } catch (error) {
        console.error(`❌ خطأ في التحقق من البوت ${botId}:`, error.message);
        mismatchCount++;
      }
    }

    // تقرير النتائج
    console.log('\n📋 تقرير التحقق:');
    console.log(`✅ سجلات متطابقة: ${matchCount}`);
    console.log(`❌ سجلات غير متطابقة: ${mismatchCount}`);

    if (mismatches.length > 0) {
      console.log('\n⚠️ الاختلافات المكتشفة:');
      mismatches.slice(0, 5).forEach(mismatch => {
        console.log(`\nبوت ${mismatch.botId}:`);
        mismatch.differences.forEach(diff => {
          console.log(`  - ${diff.field}: ${JSON.stringify(diff.original)} → ${JSON.stringify(diff.mongo)}`);
        });
      });

      if (mismatches.length > 5) {
        console.log(`\n... و ${mismatches.length - 5} اختلافات أخرى`);
      }
    }

    // إحصائيات إضافية
    console.log('\n📊 إحصائيات إضافية:');
    console.log(`🎵 بوتات نشطة: ${stats.activeBots}`);
    console.log(`👥 بوتات لها مالكين: ${stats.botsWithOwners}`);
    console.log(`😴 بوتات غير نشطة: ${stats.inactiveBots}`);

    // اختبار العمليات الأساسية
    console.log('\n🧪 اختبار العمليات الأساسية...');
    
    // اختبار إنشاء بوت جديد
    const testBotId = 'test_verification_' + Date.now();
    await musicBotService.set(testBotId, {
      volume: 50,
      repeat: false,
      owners: ['test_owner']
    });
    console.log('✅ اختبار الإنشاء: نجح');

    // اختبار القراءة
    const testData = await musicBotService.get(testBotId);
    if (testData.volume === 50) {
      console.log('✅ اختبار القراءة: نجح');
    } else {
      console.log('❌ اختبار القراءة: فشل');
    }

    // اختبار التحديث
    await musicBotService.updateField(testBotId, 'volume', 75);
    const updatedData = await musicBotService.get(testBotId);
    if (updatedData.volume === 75) {
      console.log('✅ اختبار التحديث: نجح');
    } else {
      console.log('❌ اختبار التحديث: فشل');
    }

    // اختبار الحذف
    await musicBotService.delete(testBotId);
    console.log('✅ اختبار الحذف: نجح');

    // النتيجة النهائية
    console.log('\n🎉 التحقق مكتمل!');
    if (matchCount === originalCount && mismatchCount === 0) {
      console.log('✅ الهجرة تمت بنجاح 100%');
    } else {
      const successRate = ((matchCount / originalCount) * 100).toFixed(1);
      console.log(`⚠️ معدل نجاح الهجرة: ${successRate}%`);
    }

  } catch (error) {
    console.error('❌ خطأ في التحقق:', error.message);
  } finally {
    await dbConnection.disconnect();
  }
}

// تشغيل التحقق إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  verifyMigration().catch(console.error);
}

module.exports = verifyMigration;
