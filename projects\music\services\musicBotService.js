const MusicBot = require('../models/MusicBot');
const { dbConnection } = require('../database/connection');

class MusicBotService {
  constructor() {
    this.model = MusicBot;
  }

  // التأكد من الاتصال بقاعدة البيانات
  async ensureConnection() {
    if (!dbConnection.isConnectedToDatabase()) {
      await dbConnection.connect();
    }
  }

  // الحصول على بيانات بوت (مع إنشاء تلقائي إذا لم يوجد)
  async get(botId) {
    try {
      await this.ensureConnection();
      
      let bot = await this.model.findOne({ botId });
      
      if (!bot) {
        // إنشاء بوت جديد بالبيانات الافتراضية
        bot = await this.model.findOrCreate(botId);
        console.log(`Created new bot entry for: ${botId}`);
      }
      
      return bot.toObject();
      
    } catch (error) {
      console.error(`Error getting bot data for ${botId}:`, error.message);
      // إرجاع البيانات الافتراضية في حالة الخطأ
      return this.model.getDefaultData();
    }
  }

  // حفظ/تحديث بيانات بوت
  async set(botId, data) {
    try {
      await this.ensureConnection();
      
      const bot = await this.model.findOneAndUpdate(
        { botId },
        { ...data, botId },
        { 
          new: true, 
          upsert: true, // إنشاء جديد إذا لم يوجد
          runValidators: true 
        }
      );
      
      console.log(`Updated bot data for: ${botId}`);
      return bot.toObject();
      
    } catch (error) {
      console.error(`Error setting bot data for ${botId}:`, error.message);
      throw error;
    }
  }

  // حذف بيانات بوت
  async delete(botId) {
    try {
      await this.ensureConnection();
      
      const result = await this.model.deleteOne({ botId });
      
      if (result.deletedCount > 0) {
        console.log(`Deleted bot data for: ${botId}`);
        return true;
      } else {
        console.log(`No bot data found to delete for: ${botId}`);
        return false;
      }
      
    } catch (error) {
      console.error(`Error deleting bot data for ${botId}:`, error.message);
      throw error;
    }
  }

  // الحصول على جميع البوتات
  async getAll() {
    try {
      await this.ensureConnection();
      
      const bots = await this.model.find({});
      return bots.map(bot => bot.toObject());
      
    } catch (error) {
      console.error('Error getting all bots:', error.message);
      return [];
    }
  }

  // البحث عن البوتات بناءً على معايير
  async find(criteria) {
    try {
      await this.ensureConnection();
      
      const bots = await this.model.find(criteria);
      return bots.map(bot => bot.toObject());
      
    } catch (error) {
      console.error('Error finding bots:', error.message);
      return [];
    }
  }

  // تحديث حقل محدد
  async updateField(botId, field, value) {
    try {
      await this.ensureConnection();
      
      const updateData = { [field]: value };
      const bot = await this.model.findOneAndUpdate(
        { botId },
        updateData,
        { new: true, upsert: true }
      );
      
      console.log(`Updated ${field} for bot ${botId}`);
      return bot.toObject();
      
    } catch (error) {
      console.error(`Error updating ${field} for bot ${botId}:`, error.message);
      throw error;
    }
  }

  // إضافة مالك
  async addOwner(botId, ownerId) {
    try {
      await this.ensureConnection();
      
      const bot = await this.model.findOne({ botId });
      if (!bot) {
        // إنشاء بوت جديد مع المالك
        const newBot = await this.model.findOrCreate(botId, { owners: [ownerId] });
        return newBot.toObject();
      }
      
      await bot.addOwner(ownerId);
      console.log(`Added owner ${ownerId} to bot ${botId}`);
      return bot.toObject();
      
    } catch (error) {
      console.error(`Error adding owner to bot ${botId}:`, error.message);
      throw error;
    }
  }

  // إزالة مالك
  async removeOwner(botId, ownerId) {
    try {
      await this.ensureConnection();
      
      const bot = await this.model.findOne({ botId });
      if (!bot) {
        console.log(`Bot ${botId} not found`);
        return null;
      }
      
      await bot.removeOwner(ownerId);
      console.log(`Removed owner ${ownerId} from bot ${botId}`);
      return bot.toObject();
      
    } catch (error) {
      console.error(`Error removing owner from bot ${botId}:`, error.message);
      throw error;
    }
  }

  // التحقق من المالكية
  async isOwner(botId, userId) {
    try {
      await this.ensureConnection();
      
      const bot = await this.model.findOne({ botId });
      if (!bot) {
        return false;
      }
      
      return bot.isOwner(userId);
      
    } catch (error) {
      console.error(`Error checking ownership for bot ${botId}:`, error.message);
      return false;
    }
  }

  // الحصول على البوتات بناءً على المالك
  async getBotsByOwner(ownerId) {
    try {
      await this.ensureConnection();
      
      const bots = await this.model.find({ owners: ownerId });
      return bots.map(bot => bot.toObject());
      
    } catch (error) {
      console.error(`Error getting bots for owner ${ownerId}:`, error.message);
      return [];
    }
  }

  // الحصول على البوتات بناءً على السيرفر
  async getBotsByGuild(guildId) {
    try {
      await this.ensureConnection();
      
      const bots = await this.model.find({ guildId });
      return bots.map(bot => bot.toObject());
      
    } catch (error) {
      console.error(`Error getting bots for guild ${guildId}:`, error.message);
      return [];
    }
  }

  // تحديث متعدد للبوتات
  async updateMultiple(botIds, updateData) {
    try {
      await this.ensureConnection();
      
      const result = await this.model.updateMany(
        { botId: { $in: botIds } },
        updateData
      );
      
      console.log(`Updated ${result.modifiedCount} bots`);
      return result;
      
    } catch (error) {
      console.error('Error updating multiple bots:', error.message);
      throw error;
    }
  }

  // إحصائيات
  async getStats() {
    try {
      await this.ensureConnection();
      
      const totalBots = await this.model.countDocuments();
      const activeBots = await this.model.countDocuments({ channelId: { $ne: null } });
      const botsWithOwners = await this.model.countDocuments({ owners: { $ne: [] } });
      
      return {
        totalBots,
        activeBots,
        botsWithOwners,
        inactiveBots: totalBots - activeBots
      };
      
    } catch (error) {
      console.error('Error getting stats:', error.message);
      return {
        totalBots: 0,
        activeBots: 0,
        botsWithOwners: 0,
        inactiveBots: 0
      };
    }
  }
}

// إنشاء مثيل واحد للخدمة
const musicBotService = new MusicBotService();

module.exports = musicBotService;
