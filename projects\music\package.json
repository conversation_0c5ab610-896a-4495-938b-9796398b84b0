{"name": "nodejs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "migrate": "node scripts/migrate.js", "verify": "node scripts/verify-migration.js", "test-db": "node scripts/test-connection.js", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@discordjs/voice": "^0.16.0", "@distube/deezer": "^1.0.0", "@distube/soundcloud": "^1.3.0", "@distube/spotify": "^1.5.1", "axios": "^1.3.2", "discord.js": "^14.7.1", "distube": "^4.0.4", "dotenv": "^16.0.0", "ffmpeg": "^0.0.4", "ffmpeg-static": "^4.4.1", "libsodium-wrappers": "^0.7.11", "mongoose": "^7.0.0", "node-fetch": "^3.2.6", "opusscript": "^0.0.8", "prism-media": "^1.3.5", "st.db": "^6.0.1", "tweetnacl": "^1.0.3"}}