const { dbConnection } = require('../database/connection');
const musicBotService = require('../services/musicBotService');

async function testConnection() {
  console.log('🔍 اختبار الاتصال بقاعدة البيانات MongoDB...\n');

  try {
    // اختبار الاتصال
    console.log('1️⃣ محاولة الاتصال...');
    await dbConnection.connect();
    
    // فحص حالة الاتصال
    console.log('2️⃣ فحص حالة الاتصال...');
    const connectionInfo = dbConnection.getConnectionInfo();
    console.log('📊 معلومات الاتصال:', connectionInfo);
    
    // اختبار ping
    console.log('3️⃣ اختبار ping...');
    const pingResult = await dbConnection.testConnection();
    console.log('🏓 نتيجة ping:', pingResult ? '✅ نجح' : '❌ فشل');
    
    // اختبار العمليات الأساسية
    console.log('4️⃣ اختبار العمليات الأساسية...');
    
    // إنشاء بوت تجريبي
    const testBotId = 'test_bot_' + Date.now();
    const testData = {
      volume: 75,
      repeat: true,
      channelId: '123456789',
      owners: ['test_owner_123'],
      commandsChat: '987654321'
    };
    
    console.log('📝 إنشاء بوت تجريبي...');
    await musicBotService.set(testBotId, testData);
    console.log('✅ تم إنشاء البوت التجريبي');
    
    // قراءة البيانات
    console.log('📖 قراءة البيانات...');
    const retrievedData = await musicBotService.get(testBotId);
    console.log('✅ تم استرجاع البيانات:', {
      botId: retrievedData.botId,
      volume: retrievedData.volume,
      repeat: retrievedData.repeat
    });
    
    // تحديث البيانات
    console.log('🔄 تحديث البيانات...');
    await musicBotService.updateField(testBotId, 'volume', 90);
    const updatedData = await musicBotService.get(testBotId);
    console.log('✅ تم تحديث مستوى الصوت إلى:', updatedData.volume);
    
    // إضافة مالك
    console.log('👤 إضافة مالك...');
    await musicBotService.addOwner(testBotId, 'new_owner_456');
    const dataWithNewOwner = await musicBotService.get(testBotId);
    console.log('✅ عدد المالكين الآن:', dataWithNewOwner.owners.length);
    
    // الحصول على إحصائيات
    console.log('📊 الحصول على إحصائيات...');
    const stats = await musicBotService.getStats();
    console.log('📈 الإحصائيات:', stats);
    
    // حذف البوت التجريبي
    console.log('🗑️ حذف البوت التجريبي...');
    await musicBotService.delete(testBotId);
    console.log('✅ تم حذف البوت التجريبي');
    
    console.log('\n🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل صحيح.');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاختبار:', error.message);
    console.error('📋 تفاصيل الخطأ:', error);
  } finally {
    // قطع الاتصال
    console.log('\n🔌 قطع الاتصال...');
    await dbConnection.disconnect();
    console.log('✅ تم قطع الاتصال بنجاح');
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testConnection().catch(console.error);
}

module.exports = testConnection;
