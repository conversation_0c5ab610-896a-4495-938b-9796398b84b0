// تحميل متغيرات البيئة
require('dotenv').config();

console.log('🔍 فحص متغيرات البيئة...\n');

console.log('📁 مسار العمل الحالي:', process.cwd());
console.log('📄 ملف .env موجود:', require('fs').existsSync('.env'));

console.log('\n🔗 متغيرات البيئة:');
console.log('MONGODB_URI:', process.env.MONGODB_URI ? 'موجود' : 'غير موجود');

if (process.env.MONGODB_URI) {
  // إخفاء كلمة المرور في العرض
  const hiddenUri = process.env.MONGODB_URI.replace(/\/\/.*@/, '//***:***@');
  console.log('رابط MongoDB:', hiddenUri);
  
  // التحقق من نوع الرابط
  if (process.env.MONGODB_URI.includes('localhost')) {
    console.log('⚠️ تحذير: يستخدم MongoDB محلي');
  } else if (process.env.MONGODB_URI.includes('mongodb+srv')) {
    console.log('✅ يستخدم MongoDB Atlas');
  }
  
  // التحقق من وجود اسم قاعدة البيانات
  if (process.env.MONGODB_URI.includes('/music_bots')) {
    console.log('✅ اسم قاعدة البيانات محدد: music_bots');
  } else {
    console.log('⚠️ تحذير: اسم قاعدة البيانات غير محدد');
  }
} else {
  console.log('❌ متغير MONGODB_URI غير موجود');
}

console.log('\n🧪 اختبار الاتصال...');

// اختبار الاتصال
const { dbConnection } = require('./database/connection');

async function testConnection() {
  try {
    await dbConnection.connect();
    console.log('✅ نجح الاتصال بـ MongoDB');
    
    const info = dbConnection.getConnectionInfo();
    console.log('📊 معلومات الاتصال:', info);
    
    await dbConnection.disconnect();
    console.log('✅ تم قطع الاتصال بنجاح');
    
  } catch (error) {
    console.error('❌ فشل الاتصال:', error.message);
  }
}

testConnection();
