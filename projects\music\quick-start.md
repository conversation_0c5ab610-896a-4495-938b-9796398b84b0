# 🚀 دليل البدء السريع - تحويل MongoDB

## 📋 **الخطوات السريعة**

### **1. تثبيت المكتبات:**
```bash
cd projects/music
npm install
```

### **2. إعداد MongoDB:**

#### **خيار أ: MongoDB محلي**
```bash
# تشغيل MongoDB محلياً
mongod --dbpath ./data
```

#### **خيار ب: MongoDB Atlas**
1. إنشاء حساب على [MongoDB Atlas](https://cloud.mongodb.com)
2. إنشاء cluster مجاني
3. الحصول على connection string

### **3. إعداد متغيرات البيئة:**
```bash
# نسخ ملف المثال
cp .env.example .env

# تحرير الملف
nano .env
```

**محتوى .env:**
```env
# للاستخدام المحلي
MONGODB_URI=mongodb://localhost:27017/music_bots

# أو للاستخدام مع Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/music_bots

# إعدادات أخرى
owner_id=161976265916547072
subscription_id=your_subscription_id
guild_id=your_guild_id
```

### **4. اختبار الاتصال:**
```bash
npm run test-db
```

### **5. هجرة البيانات:**
```bash
npm run migrate
```

### **6. تشغيل البوت:**
```bash
npm start
```

## 🔧 **أوامر مفيدة**

```bash
# اختبار الاتصال
npm run test-db

# هجرة البيانات
npm run migrate

# تشغيل البوت
npm start

# فحص اللوجات
tail -f logs/music-bot.log
```

## ✅ **التحقق من النجاح**

### **علامات النجاح:**
- ✅ `Successfully connected to MongoDB`
- ✅ `Migration completed successfully`
- ✅ `Bot logged in successfully`

### **في حالة الأخطاء:**
- ❌ `Connection failed` → فحص رابط MongoDB
- ❌ `Migration failed` → فحص صلاحيات الكتابة
- ❌ `Bot login failed` → فحص التوكن

## 🆘 **استكشاف الأخطاء السريع**

### **خطأ الاتصال:**
```bash
# فحص حالة MongoDB
sudo systemctl status mongod

# إعادة تشغيل MongoDB
sudo systemctl restart mongod
```

### **خطأ الهجرة:**
```bash
# فحص الملفات
ls -la databases/
ls -la projects/music/databases/

# استعادة النسخة الاحتياطية
cp databases/music_backup.json databases/music.json
```

### **خطأ البوت:**
```bash
# فحص متغيرات البيئة
cat .env

# فحص اللوجات
tail -f /var/log/mongodb/mongod.log
```

## 📊 **مراقبة الأداء**

### **فحص قاعدة البيانات:**
```javascript
// الاتصال بـ MongoDB
mongo mongodb://localhost:27017/music_bots

// عرض المجموعات
show collections

// عرض البيانات
db.musicbots.find().pretty()

// إحصائيات
db.musicbots.count()
```

### **مراقبة الذاكرة:**
```bash
# استخدام الذاكرة
free -h

# عمليات MongoDB
ps aux | grep mongo

# مساحة القرص
df -h
```

## 🔄 **العودة للنظام القديم**

```bash
# إيقاف البوت
pm2 stop all

# استعادة النسخة الاحتياطية
cp databases/music_backup.json databases/music.json

# إزالة MongoDB (اختياري)
npm uninstall mongoose dotenv

# تشغيل النظام القديم
# (تحتاج لاستعادة الكود القديم من git)
```

## 📞 **الحصول على المساعدة**

1. **فحص الوثائق:** `README_MONGODB.md`
2. **تشغيل الاختبارات:** `npm run test-db`
3. **فحص اللوجات:** في وحدة التحكم
4. **التواصل:** مع فريق التطوير

---

**نصيحة:** احتفظ بنسخة احتياطية من البيانات قبل التحويل!
